import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveBalance } from '../entities/LeaveBalance';
import { User } from '../entities/User';
import { Employee } from '../server/entities/Employee';
import { LeaveStatus } from '../types/attendance';

export class LeaveRequestController {

  /**
   * Get all leave requests with pagination and filtering
   */
  async getAll(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        department,
        leaveType,
        startDate,
        endDate,
        employeeId
      } = req.query;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      let query = leaveRequestRepository.createQueryBuilder('request')
        .leftJoinAndSelect('request.employee', 'employee')
        .leftJoinAndSelect('request.approver', 'approver');

      // Apply filters
      if (status) {
        query = query.andWhere('request.status = :status', { status });
      }

      if (department) {
        query = query.andWhere('employee.department = :department', { department });
      }

      if (leaveType) {
        query = query.andWhere('request.leaveType = :leaveType', { leaveType });
      }

      if (startDate) {
        query = query.andWhere('request.startDate >= :startDate', { startDate });
      }

      if (endDate) {
        query = query.andWhere('request.endDate <= :endDate', { endDate });
      }

      if (employeeId) {
        query = query.andWhere('request.employeeId = :employeeId', { employeeId: parseInt(employeeId as string) });
      }

      // Add pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      query = query.skip(skip).take(limitNum);

      // Order by creation date (newest first)
      query = query.orderBy('request.createdAt', 'DESC');

      const [requests, total] = await query.getManyAndCount();

      // Transform the data to include employee names
      const transformedRequests = requests.map((request: LeaveRequest) => ({
        id: request.id,
        employeeId: request.employeeId,
        employeeName: request.employee?.name || 'Unknown',
        leaveType: request.leaveType,
        startDate: request.startDate,
        endDate: request.endDate,
        daysRequested: request.daysRequested,
        reason: request.reason,
        status: request.status,
        appliedDate: request.createdAt,
        approverComments: request.approverComments,
        approverId: request.approverId,
        approverName: request.approver?.name || null,
        approvedAt: request.approvedAt,
        rejectedAt: request.rejectedAt,
        isEmergencyLeave: request.isEmergencyLeave,
        emergencyContact: request.emergencyContact,
        delegatedTo: request.delegatedTo,
        handoverNotes: request.handoverNotes,
        attachments: request.attachments ? JSON.parse(request.attachments) : []
      }));

      res.json({
        success: true,
        data: transformedRequests,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error: any) {
      console.error('Error fetching leave requests:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave requests',
        error: error.message
      });
    }
  }

  /**
   * Get a specific leave request by ID
   */
  async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      const request = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee', 'approver']
      });

      if (!request) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      const transformedRequest = {
        id: request.id,
        employeeId: request.employeeId,
        employeeName: request.employee?.name || 'Unknown',
        leaveType: request.leaveType,
        startDate: request.startDate,
        endDate: request.endDate,
        daysRequested: request.daysRequested,
        reason: request.reason,
        status: request.status,
        appliedDate: request.createdAt,
        approverComments: request.approverComments,
        approverId: request.approverId,
        approverName: request.approver?.name || null,
        approvedAt: request.approvedAt,
        rejectedAt: request.rejectedAt,
        isEmergencyLeave: request.isEmergencyLeave,
        emergencyContact: request.emergencyContact,
        delegatedTo: request.delegatedTo,
        handoverNotes: request.handoverNotes,
        attachments: request.attachments ? JSON.parse(request.attachments) : []
      };

      res.json({
        success: true,
        data: transformedRequest
      });
    } catch (error: any) {
      console.error('Error fetching leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave request',
        error: error.message
      });
    }
  }

  /**
   * Create a new leave request
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      const {
        employeeId,
        leaveType,
        startDate,
        endDate,
        reason,
        isEmergencyLeave = false,
        emergencyContact,
        delegatedTo,
        handoverNotes,
        attachments = []
      } = req.body;

      // Validate required fields
      if (!employeeId || !leaveType || !startDate || !endDate || !reason) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields'
        });
        return;
      }

      // Calculate days requested
      const start = new Date(startDate);
      const end = new Date(endDate);
      const daysRequested = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const employeeRepository = AppDataSource.getRepository('Employee');

      // Verify employee exists
      const employee = await employeeRepository.findOne({ where: { id: employeeId } });
      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
        return;
      }

      // Create the leave request
      const leaveRequest = leaveRequestRepository.create({
        employeeId,
        leaveType,
        startDate,
        endDate,
        reason,
        daysRequested,
        status: LeaveStatus.PENDING,
        isEmergencyLeave,
        emergencyContact,
        delegatedTo,
        handoverNotes,
        attachments: attachments.length > 0 ? JSON.stringify(attachments) : undefined
      });

      await leaveRequestRepository.save(leaveRequest);

      // Update leave balance (add to pending)
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const currentYear = new Date().getFullYear();
      
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId,
          leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        balance.addPending(daysRequested);
        await leaveBalanceRepository.save(balance);
      }

      res.status(201).json({
        success: true,
        data: leaveRequest,
        message: 'Leave request submitted successfully'
      });
    } catch (error: any) {
      console.error('Error creating leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create leave request',
        error: error.message
      });
    }
  }

  /**
   * Approve a leave request
   */
  async approve(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason, approverId } = req.body;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      const leaveRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      if (leaveRequest.status !== LeaveStatus.PENDING) {
        res.status(400).json({
          success: false,
          message: 'Leave request is not pending'
        });
        return;
      }

      // Update leave request
      leaveRequest.status = LeaveStatus.APPROVED;
      leaveRequest.approverComments = reason;
      leaveRequest.approverId = approverId;
      leaveRequest.approvedAt = new Date();

      await leaveRequestRepository.save(leaveRequest);

      // Update leave balance
      const currentYear = new Date().getFullYear();
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: leaveRequest.employeeId,
          leaveType: leaveRequest.leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        // Move from pending to used
        balance.removePending(leaveRequest.daysRequested);
        balance.useLeave(leaveRequest.daysRequested);
        await leaveBalanceRepository.save(balance);
      }

      res.json({
        success: true,
        data: leaveRequest,
        message: 'Leave request approved successfully'
      });
    } catch (error: any) {
      console.error('Error approving leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to approve leave request',
        error: error.message
      });
    }
  }

  /**
   * Reject a leave request
   */
  async reject(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason, approverId } = req.body;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      const leaveRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      if (leaveRequest.status !== LeaveStatus.PENDING) {
        res.status(400).json({
          success: false,
          message: 'Leave request is not pending'
        });
        return;
      }

      // Update leave request
      leaveRequest.status = LeaveStatus.REJECTED;
      leaveRequest.approverComments = reason;
      leaveRequest.approverId = approverId;
      leaveRequest.rejectedAt = new Date();

      await leaveRequestRepository.save(leaveRequest);

      // Update leave balance (remove from pending)
      const currentYear = new Date().getFullYear();
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: leaveRequest.employeeId,
          leaveType: leaveRequest.leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        balance.removePending(leaveRequest.daysRequested);
        await leaveBalanceRepository.save(balance);
      }

      res.json({
        success: true,
        data: leaveRequest,
        message: 'Leave request rejected successfully'
      });
    } catch (error: any) {
      console.error('Error rejecting leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reject leave request',
        error: error.message
      });
    }
  }

  /**
   * Cancel a leave request (by employee)
   */
  async cancel(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      const leaveRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      if (leaveRequest.status === LeaveStatus.CANCELLED) {
        res.status(400).json({
          success: false,
          message: 'Leave request is already cancelled'
        });
        return;
      }

      // Update leave request
      const originalStatus = leaveRequest.status;
      leaveRequest.status = LeaveStatus.CANCELLED;
      leaveRequest.approverComments = reason || 'Cancelled by employee';

      await leaveRequestRepository.save(leaveRequest);

      // Update leave balance based on original status
      const currentYear = new Date().getFullYear();
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: leaveRequest.employeeId,
          leaveType: leaveRequest.leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        if (originalStatus === LeaveStatus.PENDING) {
          // Remove from pending
          balance.removePending(leaveRequest.daysRequested);
        } else if (originalStatus === LeaveStatus.APPROVED) {
          // Return used days to balance by reducing the used amount
          balance.used = Math.max(0, balance.used - leaveRequest.daysRequested);
        }
        await leaveBalanceRepository.save(balance);
      }

      res.json({
        success: true,
        data: leaveRequest,
        message: 'Leave request cancelled successfully'
      });
    } catch (error: any) {
      console.error('Error cancelling leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel leave request',
        error: error.message
      });
    }
  }
} 