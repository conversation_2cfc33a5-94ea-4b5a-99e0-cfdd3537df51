import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Tab } from '@headlessui/react';
import { 
  Attendance, 
  AttendanceStatus, 
  AttendanceFilter, 
  AttendanceSettings, 
  Shift,
  AttendanceRegularizationRequest,
  LeaveRequest,
  LeaveBalance,
  LeaveStatus,
  AttendanceImportData
} from '../../../types/attendance';
import AttendanceTable from './AttendanceTable';
import AttendanceForm from './AttendanceForm';
import AttendanceReport from './AttendanceReport';
import AttendanceCalendar from './AttendanceCalendar';
import AttendanceImport from './AttendanceImport';
import TeamCalendar from './TeamCalendar';
import ShiftForm from './ShiftForm';
import ShiftAssignment, { ShiftAssignment as ShiftAssignmentType } from './ShiftAssignment';
import HolidayConfiguration from './HolidayConfiguration';
import AbsenceAnalytics from './AbsenceAnalytics';
import Portal from '../../common/Portal';
import EmployeeAttendanceModal from './EmployeeAttendanceModal';
import RegularizationManager from './RegularizationManager';

import { 
  CalendarClock, 
  Clock, 
  Calendar as CalendarIcon, 
  BarChart3, 
  Settings, 
  UserCog,
  ClipboardList, 
  Users,
  Building2,
  AlertCircle,
  Briefcase,
  X,
  Download,
  Printer,
  Filter,
  RefreshCw,
  CheckCircle,
  Home,
  Search,
  Eye,
  Upload,
  FileSpreadsheet,
  CalendarDays,
  UserCircle,
  Fingerprint,
  CircleDollarSign,
  Moon,
  Sun
} from 'lucide-react';
import AdvancedTimeAttendanceWrapper from './AdvancedTimeAttendanceWrapper';
import AttendanceService from '../../../services/AttendanceService';
import { toast } from 'react-hot-toast';
import AttendanceExport from './AttendanceExport';
import ShiftAssignmentService from '../../../services/ShiftAssignmentService';
import HolidayService, { Holiday } from '../../../services/HolidayService';
import RegularizationService from '../../../services/RegularizationService';

/**
 * AttendanceManagement Component
 * 
 * This component allows users to import attendance data from external sources
 * like Excel, CSV, or PDF files, and manage it in the system.
 * 
 * It no longer uses mock data - all attendance records come exclusively from imports.
 */
const AttendanceManagement: React.FC = () => {
  // Dark mode state and management
  const [isDarkMode, setIsDarkMode] = useState<boolean>(() => {
    // Check localStorage first, then system preference
    const savedTheme = localStorage.getItem('darkMode');
    if (savedTheme !== null) {
      return JSON.parse(savedTheme);
    }
    // Check system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  // Effect to apply dark mode to the document and persist preference
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(isDarkMode));
  }, [isDarkMode]);

  // Dark mode toggle function
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
  };

  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [allAttendances, setAllAttendances] = useState<Attendance[]>([]); // Complete unfiltered data for modal
  const [employees, setEmployees] = useState<any[]>([]); // Add employees state
  const [mergedAttendanceData, setMergedAttendanceData] = useState<any[]>([]); // Merged data for display
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Helper function to format date as YYYY-MM-DD in local timezone
  const formatLocalDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  // Helper function to convert DD/MM/YYYY format to YYYY-MM-DD format
  const convertDateFormat = (dateStr: string): string => {
    if (!dateStr) return '';
    
    // Check if already in YYYY-MM-DD format
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateStr;
    }
    
    // Convert from DD/MM/YYYY to YYYY-MM-DD
    if (dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
      const [day, month, year] = dateStr.split('/');
      return `${year}-${month}-${day}`;
    }
    
    // If date is already in some other format, try to parse it
    const parsed = new Date(dateStr);
    if (!isNaN(parsed.getTime())) {
      return formatLocalDate(parsed);
    }
    
    console.warn('Unable to convert date format:', dateStr);
    return dateStr;
  };
  
  const [selectedDate, setSelectedDate] = useState<string>(formatLocalDate(new Date())); // Default to today
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: formatLocalDate(new Date()),
    end: formatLocalDate(new Date())
  });
  const [datePeriod, setDatePeriod] = useState<string>("Today"); // Default to "Today" instead of empty string
  const [activeView, setActiveView] = useState(0);
  const [showImport, setShowImport] = useState(false);

  // New state variables for shift management
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [showShiftForm, setShowShiftForm] = useState(false);
  const [showShiftAssignment, setShowShiftAssignment] = useState(false);
  const [shiftToEdit, setShiftToEdit] = useState<Shift | undefined>(undefined);
  const [shiftAssignments, setShiftAssignments] = useState<ShiftAssignmentType[]>([]);
  const [assignmentToEdit, setAssignmentToEdit] = useState<ShiftAssignmentType | undefined>(undefined);

  // Add pagination state for shift assignments
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchAssignments, setSearchAssignments] = useState('');

  // Add new state for holiday configuration
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [weekendDays, setWeekendDays] = useState<number[]>([]); // Start with empty array, load from API

  // Add state for regularization requests
  const [regularizationRequests, setRegularizationRequests] = useState<AttendanceRegularizationRequest[]>([]);
  const [showHRRegularizationForm, setShowHRRegularizationForm] = useState(false);
  
  // Get regularization service instance
  const regularizationService = RegularizationService.getInstance();

  // Fetch all regularization requests for HR admin
  const fetchRegularizationRequests = async () => {
    try {
      const requests = await regularizationService.getAllRequests();
      setRegularizationRequests(requests);
    } catch (error) {
      console.error('Error fetching regularization requests:', error);
      toast.error('Failed to fetch regularization requests');
    }
  };
  
  // For leave management
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  
          // For Employee Portal
  const [showSelfService, setShowSelfService] = useState(false);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(null);

  // Add state for advanced attendance visibility
  const [showAdvancedFeatures, setShowAdvancedFeatures] = useState<boolean>(false);
  const [showExportModal, setShowExportModal] = useState(false);

  const [showRequestDetailsModal, setShowRequestDetailsModal] = useState(false);
  const [selectedRegularizationRequest, setSelectedRegularizationRequest] = useState<AttendanceRegularizationRequest | null>(null);

  // Add this state near other modal states
  // Holds the selected employee ID as string (from <select>) or null
  const [selectedEmployeeForReg, setSelectedEmployeeForReg] = useState<string | null>(null);

  // Update handleFiltersUpdate to do nothing or just log (or remove if unused)
  const handleFiltersUpdate = (filters: any) => {
    // No-op or just log for debugging
    // console.log('Filters updated:', filters);
  };

  // Add debugging for showImport state
  useEffect(() => {
    console.log('showImport state changed:', showImport);
  }, [showImport]);

  // Fetch all employees from the database
  const fetchEmployees = async () => {
    try {
      console.log('Fetching all employees from database...');
      const response = await fetch('/api/employees/listing');
      const data = await response.json();
      
      if (data.success && data.employees) {
        console.log(`Fetched ${data.employees.length} employees from database`);
        console.log('Sample employee data:', data.employees.slice(0, 2)); // Debug log
        
        // Filter employees to only include active and suspended status
        const activeAndSuspendedEmployees = data.employees.filter((emp: any) => {
          const status = emp.status || emp.employmentStatus || 'active';
          const normalizedStatus = status.toLowerCase();
          return normalizedStatus === 'active' || normalizedStatus === 'suspended';
        });
        
        console.log(`Filtered from ${data.employees.length} to ${activeAndSuspendedEmployees.length} employees (active/suspended only)`);
        console.log('Excluded statuses:', data.employees
          .filter((emp: any) => {
            const status = emp.status || emp.employmentStatus || 'active';
            const normalizedStatus = status.toLowerCase();
            return normalizedStatus !== 'active' && normalizedStatus !== 'suspended';
          })
          .map((emp: any) => ({ name: `${emp.firstName} ${emp.lastName}`, status: emp.status || emp.employmentStatus }))
        );
        
        // Debug: Check if employees have job/department information
        const employeesWithJobs = activeAndSuspendedEmployees.filter((emp: any) => emp.job && emp.job.department);
        console.log(`Employees with job/department info: ${employeesWithJobs.length}/${activeAndSuspendedEmployees.length}`);
        
        if (employeesWithJobs.length > 0) {
          console.log('Sample employee with job info:', employeesWithJobs[0]);
        }
        
        // Map employees to include department from job table and create a name field
        const enrichedEmployees = activeAndSuspendedEmployees.map((emp: any) => ({
          ...emp,
          name: `${emp.firstName || ''} ${emp.lastName || ''}`.trim() || `Employee ${emp.id}`,
                  department: emp.job?.department || emp.department || '',
        designation: emp.job?.designation || emp.designation || ''
        }));
        
        console.log('Enriched employees sample:', enrichedEmployees.slice(0, 2));
        
        setEmployees(enrichedEmployees);
        return enrichedEmployees;
      } else {
        console.error('Failed to fetch employees:', data.message);
        setError('Failed to fetch employees: ' + data.message);
        return [];
      }
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError('Network error while fetching employees');
      return [];
    }
  };

  // Fetch all active shifts from the database
  const fetchShifts = async () => {
    try {
      console.log('🔍 Fetching shifts from database...');
      const response = await fetch('/api/shifts');
      console.log('📡 Shift API response status:', response.status);
      
      const data = await response.json();
      console.log('📊 Shift API response data:', data);
      
      if (data.success && data.data) {
        console.log(`✅ Fetched ${data.data.length} shifts from database`);
        console.log('📋 Sample shift data:', data.data.slice(0, 2)); // Debug log
        
        // Filter to only include active shifts
        const activeShifts = data.data.filter((shift: any) => shift.isActive !== false);
        
        console.log(`🔄 Filtered from ${data.data.length} to ${activeShifts.length} active shifts`);
        console.log('🎯 Active shifts:', activeShifts.map((s: any) => ({ id: s.id, name: s.name, isActive: s.isActive })));
        
        setShifts(activeShifts);
        return activeShifts;
      } else {
        console.error('❌ Failed to fetch shifts:', data);
        setError('Failed to fetch shifts: ' + (data.message || 'Unknown error'));
        return [];
      }
    } catch (err) {
      console.error('💥 Error fetching shifts:', err);
      setError('Network error while fetching shifts');
      return [];
    }
  };

  // Refresh shifts data (for manual refresh)
  const refreshShifts = async () => {
    console.log('Manually refreshing shifts...');
    setLoading(true);
    try {
      await fetchShifts();
      toast.success('Shifts refreshed successfully');
    } catch (err) {
      console.error('Error refreshing shifts:', err);
      toast.error('Failed to refresh shifts');
    } finally {
      setLoading(false);
    }
  };

  // Fetch shift assignments with persistence
  const fetchShiftAssignments = async () => {
    try {
      console.log('Fetching shift assignments...');
      
      // Try API first, fallback to localStorage
      const apiResult = await ShiftAssignmentService.getShiftAssignments();
      
      if (apiResult.success && apiResult.data) {
        console.log(`Fetched ${apiResult.data.length} shift assignments from API`);
        setShiftAssignments(apiResult.data);
        
        // Also save to localStorage as backup
        await ShiftAssignmentService.saveToLocalStorage(apiResult.data);
        return apiResult.data;
      } else {
        console.log('API failed, trying localStorage...');
        // Fallback to localStorage
        const localData = await ShiftAssignmentService.getFromLocalStorage();
        console.log(`Fetched ${localData.length} shift assignments from localStorage`);
        setShiftAssignments(localData);
        return localData;
      }
    } catch (err) {
      console.error('Error fetching shift assignments:', err);
      
      // Final fallback to localStorage
      try {
        const localData = await ShiftAssignmentService.getFromLocalStorage();
        console.log(`Fallback: Fetched ${localData.length} shift assignments from localStorage`);
        setShiftAssignments(localData);
        return localData;
      } catch (localErr) {
        console.error('Error with localStorage fallback:', localErr);
        return [];
      }
    }
  };

  // Fetch holidays from backend with persistence
  const fetchHolidays = async () => {
    try {
      console.log('Fetching holidays from backend...');
      
      // Try API first
      const apiResult = await HolidayService.getHolidayConfiguration();
      
      if (apiResult.success && apiResult.data) {
        console.log(`Fetched ${apiResult.data.holidays.length} holidays from API`);
        console.log('🗓️ Weekend configuration from API:', apiResult.data.weekendDays);
        setHolidays(apiResult.data.holidays);
        setWeekendDays(apiResult.data.weekendDays);
        
        // Also save to localStorage as backup
        HolidayService.saveToLocalStorage(apiResult.data);
        return apiResult.data;
      } else {
        console.log('API failed, trying localStorage...');
        // Fallback to localStorage
        const localData = HolidayService.getFromLocalStorage();
        console.log(`Fetched ${localData.holidays.length} holidays from localStorage`);
        console.log('🗓️ Weekend configuration from localStorage:', localData.weekendDays);
        setHolidays(localData.holidays);
        setWeekendDays(localData.weekendDays);
        return localData;
      }
    } catch (err) {
      console.error('Error fetching holidays:', err);
      
      // Final fallback to localStorage
      try {
        const localData = HolidayService.getFromLocalStorage();
        console.log(`Fallback: Fetched ${localData.holidays.length} holidays from localStorage`);
        console.log('🗓️ Weekend configuration from fallback:', localData.weekendDays);
        setHolidays(localData.holidays);
        setWeekendDays(localData.weekendDays);
        return localData;
      } catch (localErr) {
        console.error('Error with localStorage fallback:', localErr);
        // No hardcoded defaults - system will determine weekend days dynamically
        console.log('🗓️ No weekend configuration found, using empty array');
        setHolidays([]);
        setWeekendDays([]);
        return { holidays: [], weekendDays: [] };
      }
    }
  };

  // Save shift assignments with persistence
  const saveShiftAssignments = async (assignments: ShiftAssignmentType[]) => {
    try {
      console.log('Saving shift assignments...', assignments.length);
      
      // Try API first
      const apiResult = await ShiftAssignmentService.createShiftAssignments(assignments);
      
      if (apiResult.success) {
        console.log('Successfully saved to API');
        // Update state with server response
        if (apiResult.data) {
          setShiftAssignments(prev => [...prev, ...apiResult.data!]);
        }
        
        // Also save to localStorage as backup
        await ShiftAssignmentService.saveToLocalStorage([...shiftAssignments, ...assignments]);
        
        toast.success(`Successfully assigned shifts to ${assignments.length} employees`);
      } else {
        console.log('API save failed, saving to localStorage only');
        
        // Fallback to localStorage only
        const newAssignments = [...shiftAssignments, ...assignments];
        setShiftAssignments(newAssignments);
        await ShiftAssignmentService.saveToLocalStorage(newAssignments);
        
        toast.success(`Shift assignments saved locally (${assignments.length} employees)`);
        toast.error('Could not sync with server - assignments saved locally only', { 
          duration: 5000,
          style: { backgroundColor: '#f59e0b', color: 'white' }
        });
      }
    } catch (err) {
      console.error('Error saving shift assignments:', err);
      
      // Emergency fallback - just save locally
      try {
        const newAssignments = [...shiftAssignments, ...assignments];
        setShiftAssignments(newAssignments);
        await ShiftAssignmentService.saveToLocalStorage(newAssignments);
        
        toast.success(`Shift assignments saved locally (${assignments.length} employees)`);
        toast.error('Server error - assignments saved locally only');
      } catch (localErr) {
        console.error('Even localStorage failed:', localErr);
        toast.error('Failed to save shift assignments');
      }
    }
  };

  // Remove a shift assignment with persistence
  const removeShiftAssignment = async (index: number) => {
    try {
      const assignmentToRemove = shiftAssignments[index];
      
      if (assignmentToRemove.id) {
        // Try to remove from API if it has an ID
        const apiResult = await ShiftAssignmentService.deleteShiftAssignment(assignmentToRemove.id);
        
        if (apiResult.success) {
          console.log('Successfully removed from API');
        } else {
          console.log('API removal failed, removing locally only');
        }
      }
      
      // Remove from local state
      const newAssignments = shiftAssignments.filter((_, i) => i !== index);
      setShiftAssignments(newAssignments);
      
      // Update localStorage
      await ShiftAssignmentService.saveToLocalStorage(newAssignments);
      
      const employee = employees.find(emp => emp.id === assignmentToRemove.employeeId);
      const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : `Employee ${assignmentToRemove.employeeId}`;
      
      toast.success(`Removed shift assignment for ${employeeName}`);
      
    } catch (err) {
      console.error('Error removing shift assignment:', err);
      toast.error('Failed to remove shift assignment');
    }
  };

  // Merge employees with attendance data for display
  const mergeEmployeesWithAttendance = (employees: any[], attendances: Attendance[], dateFilter: { start: string; end: string }) => {
    // Only log when there are significant data changes or when explicitly needed
    const shouldLog = employees.length > 50 && attendances.length > 0; // Only log for larger datasets
    
    if (shouldLog) {
      console.log(`Merging ${employees.length} employees with ${attendances.length} attendance records for period ${dateFilter.start} to ${dateFilter.end}`);
    }
    
    // Handle date filtering
    let filteredAttendances = attendances;
    if (dateFilter.start && dateFilter.end) {
      filteredAttendances = attendances.filter(att => 
        att.date >= dateFilter.start && att.date <= dateFilter.end
      );
    }
    
    // Group attendance records by employee
    const attendanceByEmployee = filteredAttendances.reduce((acc, attendance) => {
      const empId = attendance.employeeId;
      if (!acc[empId]) {
        acc[empId] = [];
      }
      acc[empId].push(attendance);
      return acc;
    }, {} as Record<number, Attendance[]>);
    
    // Only log summary information for significant datasets
    if (shouldLog) {
      console.log(`Grouped into ${Object.keys(attendanceByEmployee).length} employees with attendance records`);
    }
    
    // Check for ID mismatches (only for larger datasets)
    if (shouldLog) {
      const attendanceEmployeeIds = Object.keys(attendanceByEmployee).map(id => parseInt(id));
      const databaseEmployeeIds = employees.map(emp => emp.id);
      
      const missingInDatabase = attendanceEmployeeIds.filter(id => !databaseEmployeeIds.includes(id));
      
      if (missingInDatabase.length > 0) {
        console.warn(`⚠️ ${missingInDatabase.length} employee IDs in attendance but not in database`);
      }
    }
    
    // Create merged records - one row per employee per day
    const mergedRecords: any[] = [];
    
    // First, add employees who have attendance records
    Object.entries(attendanceByEmployee).forEach(([empIdStr, empAttendances]) => {
      const empId = parseInt(empIdStr);
      const employee = employees.find(emp => emp.id === empId);
      
      // Sort attendance records by date
      empAttendances.sort((a, b) => a.date.localeCompare(b.date));
      
      empAttendances.forEach(attendance => {
        const mergedRecord = {
          id: attendance.id,
          employeeId: attendance.employeeId,
          employeeCode: employee?.employeeId || String(attendance.employeeId),
          employeeName: attendance.employeeName || (employee ? `${employee.firstName || ''} ${employee.lastName || ''}`.trim() : 'Unknown'),
          // Always prioritize employee database department for consistency
                      department: employee?.department || attendance.department || '',
            employeeDepartment: employee?.department || attendance.department || '',
            designation: employee?.designation || attendance.position || '',
          date: attendance.date,
          checkInTime: attendance.checkInTime,
          checkOutTime: attendance.checkOutTime,
          status: attendance.status,
          workHours: attendance.workHours,
                      overtime: attendance.overtime || null,
                      location: attendance.location || '',
          notes: attendance.notes,
          isRemote: attendance.isRemote || false,
                      position: attendance.position || employee?.designation || '',
                      shift: attendance.shift || null,
          isImported: attendance.isImported || false,
          hasAttendanceRecord: true,
          employeePhoto: employee?.profileImagePath || null,
          isRegularized: attendance.isRegularized || false
        };
        
        mergedRecords.push(mergedRecord);
      });
    });
    
    // Add employees without attendance records for the date range
    employees.forEach(employee => {
      const hasRecord = attendanceByEmployee[employee.id];
      if (!hasRecord) {
        // For date ranges, create one record per day in the range
        const startDate = new Date(dateFilter.start);
        const endDate = new Date(dateFilter.end);
        
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
          const dateStr = d.toISOString().split('T')[0];
          
          mergedRecords.push({
            id: `emp-${employee.id}-${dateStr}`,
            employeeId: employee.id,
            employeeCode: employee.employeeId || String(employee.id),
            employeeName: `${employee.firstName || ''} ${employee.lastName || ''}`.trim() || 'Unknown',
            // Use employee database department as source of truth
                          department: employee.department || '',
              employeeDepartment: employee.department || '',
              designation: employee.designation || '',
            date: dateStr,
            checkInTime: null,
            checkOutTime: null,
            status: 'ABSENT',
            workHours: null,
                          overtime: null,
                          location: '',
            notes: null,
            isRemote: false,
                          position: employee.designation || '',
                          shift: null,
            isImported: false,
            hasAttendanceRecord: false,
            employeePhoto: employee.profileImagePath || null,
            isRegularized: false
          });
        }
      }
    });
    
    // Sort by employee name, then by date
    mergedRecords.sort((a, b) => {
      const nameCompare = a.employeeName.localeCompare(b.employeeName);
      if (nameCompare !== 0) return nameCompare;
      return a.date.localeCompare(b.date);
    });
    
    // Only log final result for significant datasets
    if (shouldLog) {
      console.log(`✅ Created ${mergedRecords.length} merged records for display`);
    }
    
    return mergedRecords;
  };

  // Add useEffect to merge data when employees, attendances, or dateRange changes
  // Use useMemo to prevent excessive re-calculations
  const computedMergedData = useMemo(() => {
    if (employees.length > 0) {
      const merged = mergeEmployeesWithAttendance(employees, attendances, dateRange);
      return merged;
    }
    return [];
  }, [employees, attendances, dateRange]);

  // Update state only when computedMergedData changes
  useEffect(() => {
    setMergedAttendanceData(computedMergedData);
  }, [computedMergedData]);

  // Add useEffect to fetch attendance data when dateRange changes
  // Removed duplicate call - data refresh is handled by the merge effect above
  useEffect(() => {
    if (dateRange.start && dateRange.end) {
      refreshAttendanceData();
    }
  }, [dateRange.start, dateRange.end]);

  // Professional system: Load data from database only
  useEffect(() => {
    console.log('🏢 Professional System: Loading data from database...');
    
    // Load both employees and attendance data
    const initializeData = async () => {
      await fetchEmployees();
      await fetchShifts();
      await fetchShiftAssignments(); // Re-enabled with improved error handling
      await fetchHolidays(); // Add holiday fetching
      await fetchRegularizationRequests(); // Add regularization requests fetching
   
      await refreshAttendanceData();
    };
    
    initializeData();
  }, []); // Empty dependency array - only run once on mount

  const handleImportAttendance = async (importedData: AttendanceImportData[]) => {
    console.log('Importing attendance data:', importedData.length, 'records');
    
    try {
      setLoading(true);
      
      // Create properly formatted data with all required fields
      const formattedData = importedData.map(record => ({
        ...record,
        // Ensure all required fields are present
        id: record.id || Math.floor(Math.random() * 1000000),
        employeeId: typeof record.employeeId === 'string' ? parseInt(record.employeeId) : (record.employeeId || 0),
        employeeName: record.employeeName || 'Unknown',
        date: record.date || new Date().toISOString().split('T')[0],
        status: (record.status as AttendanceStatus) || AttendanceStatus.PRESENT,
        workHours: record.workHours || null
      }));
      
      // Show a visual indication immediately that we're processing
      toast.loading('Saving attendance data to database...');
      
      // Save the data to the backend
      console.log('Saving imported data to database...');
      const result = await AttendanceService.importAttendances(formattedData);
      
      // Dismiss the loading toast
      toast.dismiss();
      
      if (result.error) {
        console.error('❌ Database save failed:', result.error);
        toast.error(`Import Failed: ${result.error}`, { duration: 8000 });
        return; // Stop processing if database save failed
      } else {
        console.log('Import successful:', result.data);
        
        // Professional import feedback with detailed validation results
        const results = result.data?.results;
        const summary = results?.summary;
        
        if (summary) {
          let successMessage = `✅ Database Import Complete: ${summary.saved} records saved`;
          
          if (summary.skipped > 0) {
            successMessage += `\n⚠️ ${summary.skipped} records skipped (employees not found in database)`;
          }
          
          if (summary.failed > 0) {
            successMessage += `\n❌ ${summary.failed} records failed validation`;
          }
          
          toast.success(successMessage, { 
            duration: 6000,
            style: { maxWidth: '500px' }
          });
          
                     // Show detailed information about skipped employees
           if (results.skippedEmployees?.length > 0) {
             const skippedDetails = results.skippedEmployees
               .map((emp: any) => `ID ${emp.employeeId} (${emp.employeeName || 'Unknown'}) - ${emp.recordCount} records`)
               .join('\n');
             
             toast.error(`🚫 Employees Not Found in Database:\n${skippedDetails}`, {
               duration: 10000,
               style: { maxWidth: '600px' }
             });
           }
           
           // Show detailed errors if any
           if (results.errors?.length > 0) {
             console.warn('Import errors:', results.errors);
             const errorsByReason = results.errors.reduce((acc: Record<string, any[]>, err: any) => {
               if (!acc[err.reason]) acc[err.reason] = [];
               acc[err.reason].push(err);
               return acc;
             }, {} as Record<string, any[]>);
             
             Object.entries(errorsByReason).forEach(([reason, errors]) => {
               const errorSummary = (errors as any[]).slice(0, 3).map((err: any) => 
                 `Employee ${err.record.employeeId}: ${err.error}`
               ).join('\n');
               
               toast.error(`${reason}:\n${errorSummary}${(errors as any[]).length > 3 ? '\n...and more' : ''}`, { 
                 duration: 8000,
                 style: { maxWidth: '600px' }
               });
             });
           }
        } else {
          toast.success(`Successfully imported ${results?.success || formattedData.length} attendance records`);
        }
      }
      
      // Refresh data from database to show only what was actually saved
      console.log('Refreshing data from database after import...');
      await refreshAttendanceData();
      
      // Don't automatically set selectedDate - let user choose the date to view
      
      // Clear any previous errors
      setError(null);
      
    } catch (err) {
      console.error('Error in import process:', err);
      toast.error('An error occurred during import');
    } finally {
      setLoading(false);
      setShowImport(false);
    }
  };

  const refreshAttendanceData = useCallback(async () => {
    console.log(`📊 Refreshing attendance data for ${dateRange.start} to ${dateRange.end}...`);
    try {
      setLoading(true);
      
      // First, always fetch ALL attendance data for the modal
      const allResult = await AttendanceService.getAttendances();
      
      if (allResult.data && Array.isArray(allResult.data)) {
        setAllAttendances(allResult.data); // Store complete unfiltered data
        console.log(`✅ Loaded ${allResult.data.length} total attendance records`);
      }
      
      // Convert date formats from DD/MM/YYYY to YYYY-MM-DD for backend API
      const convertedStartDate = convertDateFormat(dateRange.start);
      const convertedEndDate = convertDateFormat(dateRange.end);
      
      console.log(`🔄 Converting dates: ${dateRange.start} → ${convertedStartDate}, ${dateRange.end} → ${convertedEndDate}`);
      
      // Debug: Check if we have May data in allAttendances
      if (allResult.data && Array.isArray(allResult.data)) {
        const mayRecords = allResult.data.filter((a: Attendance) => a.date >= '2025-05-01' && a.date <= '2025-05-31');
        console.log(`🔍 Found ${mayRecords.length} records for May 2025 in allAttendances:`, mayRecords.slice(0, 3));
        
        // Manual filter to test if client-side filtering works
        const manualFilter = allResult.data.filter((a: Attendance) => 
          a.date >= convertedStartDate && a.date <= convertedEndDate
        );
        console.log(`🧪 Manual client-side filter result: ${manualFilter.length} records`);
      }
      
      // Then fetch filtered data for the main table view
      const result = await AttendanceService.getAttendances({
        startDate: convertedStartDate, 
        endDate: convertedEndDate
      });
      
      console.log(`📡 Backend API response:`, result);
      
      if (result.error) {
        console.error('Error refreshing attendance data:', result.error);
        setError('Failed to refresh attendance data: ' + result.error);
      } else if (result.data && Array.isArray(result.data)) {
        console.log(`✅ Filtered to ${result.data.length} records for selected period`);
        console.log(`📋 Sample filtered records:`, result.data.slice(0, 3));
        setAttendances(result.data);
        setError(null);
        
        // If no filtered data but we have all data, show available dates info
        if (result.data.length === 0 && allResult.data && allResult.data.length > 0) {
          const availableDates = [...new Set(allResult.data.map((a: Attendance) => a.date))].sort();
          if (availableDates.length > 0) {
            const latestDate = availableDates[availableDates.length - 1];
            console.log(`ℹ️ No data for selected range. Latest available: ${latestDate} (${availableDates.length} dates total)`);
            console.log(`📅 Available date range: ${availableDates[0]} to ${latestDate}`);
            
            // Try client-side filter as fallback
            const clientSideFiltered = allResult.data.filter((a: Attendance) => 
              a.date >= convertedStartDate && a.date <= convertedEndDate
            );
            if (clientSideFiltered.length > 0) {
              console.log(`🔧 FALLBACK: Using client-side filter, found ${clientSideFiltered.length} records`);
              setAttendances(clientSideFiltered);
            }
          }
        }
      } else {
        console.error('Unexpected data format received:', result.data);
      }
    } catch (err) {
      console.error('Error refreshing attendance data:', err);
      setError('Network error while fetching attendance data');
    } finally {
      setLoading(false);
    }
  }, [dateRange.start, dateRange.end]);

  const handleClearImportedData = async () => {
    if (!confirm('Are you sure you want to clear all imported attendance data? This action cannot be undone.')) {
        return;
      }
      
    try {
      setLoading(true);
      toast.loading('Clearing imported attendance data...');
      
      const result = await AttendanceService.clearImportedAttendances();
      
      toast.dismiss();
      
      if (result.error) {
        console.error('Error clearing imported data:', result.error);
        toast.error('Failed to clear imported data: ' + result.error);
      } else {
        console.log('Successfully cleared imported data:', result.data);
        toast.success(`Successfully cleared ${result.data?.deletedCount || 0} imported attendance records`);
        
        // Refresh the data to show updated state
        await refreshAttendanceData();
      }
    } catch (err) {
      console.error('Error clearing imported data:', err);
      toast.error('An error occurred while clearing imported data');
    } finally {
      setLoading(false);
    }
  };

  const handleClearAllData = async () => {
    if (!confirm('⚠️ WARNING: This will delete ALL attendance data (imported and manual). Are you absolutely sure? This action cannot be undone.')) {
      return;
    }

    // Double confirmation for destructive action
    if (!confirm('This is your final confirmation. All attendance records will be permanently deleted. Continue?')) {
      return;
    }

    try {
      setLoading(true);
      toast.loading('Clearing all attendance data...');
      
      const result = await AttendanceService.clearAllAttendances();
      
      toast.dismiss();
      
      if (result.error) {
        console.error('Error clearing all data:', result.error);
        toast.error('Failed to clear all data: ' + result.error);
      } else {
        console.log('Successfully cleared all data:', result.data);
        toast.success(`Successfully cleared all ${result.data?.deletedCount || 0} attendance records`);
        
        // Clear the UI immediately
        setAttendances([]);
        setError(null);
        
        // Also clear localStorage
        localStorage.removeItem('attendance_data_temp');
      }
    } catch (err) {
      console.error('Error clearing all data:', err);
      toast.error('An error occurred while clearing all data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (date: string) => {
    console.log('📅 Calendar date selected:', date);
    setSelectedDate(date);
    
    // Update the date range to show just this single date for the table
    setDateRange({ start: date, end: date });
    
    // Detect if the selected date corresponds to a predefined period
    const today = formatLocalDate(new Date());
    const yesterday = formatLocalDate(new Date(Date.now() - 24 * 60 * 60 * 1000));
    
    let detectedPeriod = "User Defined";
    
    if (date === today) {
      detectedPeriod = "Today";
    } else if (date === yesterday) {
      detectedPeriod = "Yesterday";
    } else {
      // Check if it's part of this week
      const now = new Date();
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // End of current week (Saturday)
      
      const selectedDate = new Date(date);
      if (selectedDate >= startOfWeek && selectedDate <= endOfWeek) {
        detectedPeriod = "This Week";
      } else {
        // Check if it's part of this month
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        if (selectedDate >= startOfMonth && selectedDate <= endOfMonth) {
          detectedPeriod = "This Month";
        }
      }
    }
    
    // Update the date period to the detected period
    setDatePeriod(detectedPeriod);
    
    // Log for debugging the integration
    console.log('🔗 Calendar linked to table - filtering attendance for:', date);
    console.log('📅 Detected date period:', detectedPeriod);
  };

  const handleDateRangeChange = (range: { start: string; end: string }) => {
    console.log('Date range changed to:', range);
    setDateRange(range);
    
    // Don't automatically update selectedDate - let user manually select dates
  };

  const handleDatePeriodChange = (period: string) => {
    console.log('Date period changed to:', period);
    setDatePeriod(period);
  };

  const handleAddAttendance = async (attendance: Omit<Attendance, 'id'>) => {
    try {
      console.log('📝 Creating new attendance record:', attendance);
      
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(attendance),
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        console.log('✅ Successfully created attendance:', result.data);
        
        // Add to local state with the server-generated ID
        setAttendances(Array.isArray(attendances) ? [...attendances, result.data as Attendance] : [result.data as Attendance]);
        
        // Show success message
        const employeeName = attendance.employeeName || 
          (() => {
            const employee = employees.find(emp => emp.id === attendance.employeeId);
            return employee?.name || `Employee ID: ${attendance.employeeId}`;
          })();
        toast.success(`Attendance marked successfully for ${employeeName}`);
      } else {
        console.error('❌ Failed to create attendance:', result.message);
        toast.error('Failed to mark attendance: ' + (result.message || 'Unknown error'));
        
        // Fallback to local update if API fails
        const newAttendance = {
          ...attendance,
          id: Math.max(...(Array.isArray(attendances) ? attendances.map(a => a.id) : [0]), 0) + 1
        };
        setAttendances(Array.isArray(attendances) ? [...attendances, newAttendance as Attendance] : [newAttendance as Attendance]);
        toast.success('⚠️ Attendance saved locally only - server connection failed');
      }
    } catch (error) {
      console.error('❌ Error creating attendance:', error);
      toast.error('Network error while marking attendance');
      
      // Emergency fallback - save locally
      const newAttendance = {
        ...attendance,
        id: Math.max(...(Array.isArray(attendances) ? attendances.map(a => a.id) : [0]), 0) + 1
      };
      setAttendances(Array.isArray(attendances) ? [...attendances, newAttendance as Attendance] : [newAttendance as Attendance]);
      toast.success('⚠️ Attendance saved locally only - please check your connection');
    }
  };

  const handleUpdateAttendance = async (updatedAttendance: Attendance) => {
    try {
      console.log('📝 Updating attendance record:', updatedAttendance);
      
      const response = await fetch(`/api/attendance/${updatedAttendance.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedAttendance),
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        console.log('✅ Successfully updated attendance:', result.data);
        
        // Update local state with server response
        if (!Array.isArray(attendances)) {
          setAttendances([result.data]);
        } else {
          setAttendances(
            attendances.map(attendance => 
              attendance.id === updatedAttendance.id ? result.data : attendance
            )
          );
        }
        
        const employeeName = updatedAttendance.employeeName || 
          (() => {
            const employee = employees.find(emp => emp.id === updatedAttendance.employeeId);
            return employee?.name || `Employee ID: ${updatedAttendance.employeeId}`;
          })();
        toast.success(`Attendance updated successfully for ${employeeName}`);
      } else {
        console.error('❌ Failed to update attendance:', result.message);
        toast.error('Failed to update attendance: ' + (result.message || 'Unknown error'));
        
        // Fallback to local update if API fails
        if (!Array.isArray(attendances)) {
          setAttendances([updatedAttendance]);
        } else {
          setAttendances(
            attendances.map(attendance => 
              attendance.id === updatedAttendance.id ? updatedAttendance : attendance
            )
          );
        }
        toast.success('⚠️ Attendance updated locally only - server connection failed');
      }
    } catch (error) {
      console.error('❌ Error updating attendance:', error);
      toast.error('Network error while updating attendance');
      
      // Emergency fallback - update locally
      if (!Array.isArray(attendances)) {
        setAttendances([updatedAttendance]);
      } else {
        setAttendances(
          attendances.map(attendance => 
            attendance.id === updatedAttendance.id ? updatedAttendance : attendance
          )
        );
      }
      toast.success('⚠️ Attendance updated locally only - please check your connection');
    }
  };

  const handleDeleteAttendance = (id: number) => {
    if (!Array.isArray(attendances)) {
      setAttendances([]);
      return;
    }
    
    setAttendances(attendances.filter(attendance => attendance.id !== id));
  };

  // Add new handlers for shift management with API calls
  const handleAddShift = async (shiftData: Omit<Shift, 'id'>) => {
    try {
      console.log('Creating new shift:', shiftData);
      
      const response = await fetch('/api/shifts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shiftData),
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        console.log('Successfully created shift:', result.data);
        
        // Add to local state
        setShifts(prevShifts => [...prevShifts, result.data]);
        setShowShiftForm(false);
        
        toast.success(`Successfully created shift "${result.data.name}"`);
      } else {
        console.error('Failed to create shift:', result.message);
        toast.error('Failed to create shift: ' + (result.message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error creating shift:', err);
      toast.error('Network error while creating shift');
    }
  };

  const handleUpdateShift = async (shiftData: Omit<Shift, 'id'>) => {
    if (!shiftToEdit) return;
    
    try {
      console.log('Updating shift:', shiftToEdit.id, shiftData);
      
      const response = await fetch(`/api/shifts/${shiftToEdit.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shiftData),
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        console.log('Successfully updated shift:', result.data);
        
        // Update local state
        setShifts(prevShifts => 
          prevShifts.map(shift => 
            shift.id === shiftToEdit.id ? result.data : shift
          )
        );
        setShowShiftForm(false);
        setShiftToEdit(undefined);
        
        toast.success(`Successfully updated shift "${result.data.name}"`);
      } else {
        console.error('Failed to update shift:', result.message);
        toast.error('Failed to update shift: ' + (result.message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error updating shift:', err);
      toast.error('Network error while updating shift');
    }
  };

  const handleDeleteShift = async (id: number) => {
    const shiftToDelete = shifts.find(s => s.id === id);
    
    if (!confirm(`Are you sure you want to delete the shift "${shiftToDelete?.name}"? This action cannot be undone.`)) {
      return;
    }
    
    try {
      console.log('Deleting shift:', id);
      
      const response = await fetch(`/api/shifts/${id}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log('Successfully deleted shift:', id);
        
        // Remove from local state
        setShifts(prevShifts => prevShifts.filter(shift => shift.id !== id));
        
        toast.success(`Successfully deleted shift "${shiftToDelete?.name}"`);
      } else {
        console.error('Failed to delete shift:', result.message);
        toast.error('Failed to delete shift: ' + (result.message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error deleting shift:', err);
      toast.error('Network error while deleting shift');
    }
  };

  const handleToggleShiftStatus = async (id: number) => {
    try {
      console.log('Toggling shift status:', id);
      
      const response = await fetch(`/api/shifts/${id}/toggle-status`, {
        method: 'PATCH',
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        console.log('Successfully toggled shift status:', result.data);
        
        // Update local state
        setShifts(prevShifts => 
          prevShifts.map(shift => 
            shift.id === id ? result.data : shift
          )
        );
        
        const status = result.data.isActive ? 'activated' : 'deactivated';
        toast.success(`Successfully ${status} shift "${result.data.name}"`);
      } else {
        console.error('Failed to toggle shift status:', result.message);
        toast.error('Failed to toggle shift status: ' + (result.message || 'Unknown error'));
      }
    } catch (err) {
      console.error('Error toggling shift status:', err);
      toast.error('Network error while toggling shift status');
    }
  };

  const handleEditShift = (shift: Shift) => {
    setShiftToEdit(shift);
    setShowShiftForm(true);
  };

  const handleAssignShifts = (assignments: ShiftAssignmentType[]) => {
    if (assignmentToEdit) {
      // Update existing assignment
      updateShiftAssignment(assignments[0]); // Only one assignment when editing
    } else {
      // Create new assignments
      saveShiftAssignments(assignments);
    }
    setShowShiftAssignment(false);
    setAssignmentToEdit(undefined);
  };

  // Add handler for editing assignment
  const handleEditAssignment = (assignment: ShiftAssignmentType) => {
    setAssignmentToEdit(assignment);
    setShowShiftAssignment(true);
  };

  // Update shift assignment function
  const updateShiftAssignment = async (updatedAssignment: ShiftAssignmentType) => {
    try {
      console.log('Updating shift assignment...', updatedAssignment);
      
      // Find the assignment in the current array
      const assignmentIndex = shiftAssignments.findIndex(a => 
        a.employeeId === assignmentToEdit?.employeeId && 
        a.shiftId === assignmentToEdit?.shiftId &&
        a.startDate === assignmentToEdit?.startDate
      );

      if (assignmentIndex === -1) {
        toast.error('Assignment not found');
        return;
      }

      // Try API first if the assignment has an ID
      if (assignmentToEdit?.id) {
        const apiResult = await ShiftAssignmentService.updateShiftAssignment(assignmentToEdit.id, updatedAssignment);
        
        if (apiResult.success) {
          console.log('Successfully updated assignment via API');
        } else {
          console.log('API update failed, updating locally only');
        }
      }
      
      // Update local state
      const newAssignments = [...shiftAssignments];
      newAssignments[assignmentIndex] = { ...updatedAssignment, id: assignmentToEdit?.id };
      setShiftAssignments(newAssignments);
      
      // Update localStorage
      await ShiftAssignmentService.saveToLocalStorage(newAssignments);
      
      const employee = employees.find(emp => emp.id === updatedAssignment.employeeId);
      const employeeName = employee ? `${employee.firstName} ${employee.lastName}` : `Employee ${updatedAssignment.employeeId}`;
      
      toast.success(`Updated shift assignment for ${employeeName}`);
      
    } catch (err) {
      console.error('Error updating shift assignment:', err);
      toast.error('Failed to update shift assignment');
    }
  };

  // Add a handler for saving holiday configuration
  const handleSaveHolidayConfig = async (data: { holidays: Holiday[], weekendDays: number[] }) => {
    try {
      console.log('Saving holiday configuration...', data);
      
      // Try to save to API first
      const apiResult = await HolidayService.saveHolidayConfiguration(data);
      
      if (apiResult.success) {
        console.log('Successfully saved holiday configuration to API');
        setHolidays(data.holidays);
        setWeekendDays(data.weekendDays);
        
        // Also save to localStorage as backup
        HolidayService.saveToLocalStorage(data);
        
        toast.success('Holiday configuration saved successfully');
      } else {
        console.log('API save failed, saving to localStorage only');
        
        // Fallback to localStorage only
        setHolidays(data.holidays);
        setWeekendDays(data.weekendDays);
        HolidayService.saveToLocalStorage(data);
        
        toast.success('Holiday configuration saved locally');
        toast.error('Could not sync with server - configuration saved locally only', { 
          duration: 5000,
          style: { backgroundColor: '#f59e0b', color: 'white' }
        });
      }
    } catch (err) {
      console.error('Error saving holiday configuration:', err);
      
      // Emergency fallback - just save locally
      try {
        setHolidays(data.holidays);
        setWeekendDays(data.weekendDays);
        HolidayService.saveToLocalStorage(data);
        
        toast.success('Holiday configuration saved locally');
        toast.error('Server error - configuration saved locally only');
      } catch (localErr) {
        console.error('Even localStorage failed:', localErr);
        toast.error('Failed to save holiday configuration');
      }
    }
  };

  const handleSelfServiceOpen = (employeeId: number) => {
    setSelectedEmployeeId(employeeId);
    setShowSelfService(true);
    
    // Leave balances should be loaded from the database/API
    // No hardcoded initialization - all leave types and balances come from backend
  };
  
  const handleSelfServiceClose = () => {
    setShowSelfService(false);
    setSelectedEmployeeId(null);
  };
  
  const handleSubmitRegularization = async (request: Omit<AttendanceRegularizationRequest, 'id'>) => {
    try {
      const newRequest = await regularizationService.submitRequest(request);
 
      
      // Show confirmation or notification
      toast.success('Your regularization request has been submitted successfully!');
    } catch (error) {
      console.error('Error submitting regularization request:', error);
      toast.error('Failed to submit regularization request. Please try again.');
    }
  };

  // Handle approve regularization request
  const handleApproveRegularization = async (id: number, comments?: string) => {
    try {
      await regularizationService.approveRequest(id, comments);
     
      // Refresh the requests list to show updated status
      await fetchRegularizationRequests();
      
      toast.success('Regularization request approved successfully!');
    } catch (error) {
      console.error('Error approving regularization request:', error);
      toast.error('Failed to approve request. Please try again.');
    }
  };

  // Handle reject regularization request
  const handleRejectRegularization = async (id: number, comments?: string) => {
    try {
      await regularizationService.rejectRequest(id, comments || '');
      
      // Refresh the requests list to show updated status
      await fetchRegularizationRequests();
      
      toast.success('Regularization request rejected.');
    } catch (error) {
      console.error('Error rejecting regularization request:', error);
      toast.error('Failed to reject request. Please try again.');
    }
  };

  // Handle view regularization details
  const handleViewRegularizationDetails = (request: AttendanceRegularizationRequest) => {
    setSelectedRegularizationRequest(request);
    setShowRequestDetailsModal(true);
  };

  // Handle HR add regularization request
  const handleAddRegularizationRequest = () => {
    setShowHRRegularizationForm(true);
  };

  // Handle HR regularization form submission
  const handleHRRegularizationSubmit = async (request: Omit<AttendanceRegularizationRequest, 'id'>) => {
    try {
      await regularizationService.submitRequest(request);
      setShowHRRegularizationForm(false);
      toast.success('Regularization request submitted successfully');
      
      // Refresh the requests list to show the new request
      await fetchRegularizationRequests();
  
    } catch (error) {
      console.error('Error submitting regularization request:', error);
      toast.error('Failed to submit regularization request');
    }
  };

  
  const handleSubmitLeaveRequest = (request: Omit<LeaveRequest, 'id'>) => {
    const newRequest = {
      ...request,
      id: Math.max(...leaveRequests.map(r => r.id || 0), 0) + 1
    };
    
    setLeaveRequests([...leaveRequests, newRequest as LeaveRequest]);
    
    // In a real application, this would make an API call
    // and update the leave balances accordingly
    
    // Mock updating leave balances
    const updatedLeaveBalances = [...leaveBalances];
    const balanceIndex = updatedLeaveBalances.findIndex(
      b => b.employeeId === request.employeeId && b.leaveType === request.leaveType
    );
    
    if (balanceIndex >= 0) {
      // Calculate the number of days
      const start = new Date(request.startDate);
      const end = new Date(request.endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      
      // Adjust for half-day if applicable
      if (request.isHalfDay && request.startDate === request.endDate) {
        diffDays = 0.5;
      }
      
      updatedLeaveBalances[balanceIndex] = {
        ...updatedLeaveBalances[balanceIndex],
        pending: updatedLeaveBalances[balanceIndex].pending + diffDays,
        remaining: updatedLeaveBalances[balanceIndex].remaining - diffDays
      };
      
      setLeaveBalances(updatedLeaveBalances);
    }
  };

  // Add configuration for which tabs should hide the statistics cards
  const tabsToHideStats = [
    1,  // Mark Attendance
    2,  // Calendar View
    3,  // Reports
    4,  // Regularization
    5,  // Shifts
    6,  // Team Calendar
    7,  // Holidays
    8,  // Employee Records
    9   // Advanced Time
  ];

  // Add function to toggle advanced features
  const toggleAdvancedFeatures = () => {
    // Sync any necessary data before switching views
    if (showAdvancedFeatures) {
      // Import data from advanced system back to standard system if needed
      refreshAttendanceData();
    }
    setShowAdvancedFeatures(!showAdvancedFeatures);
  };

  const renderStatCards = () => {
    const today = new Date().toISOString().split('T')[0];
    
    // Get today's data only for statistics
    const todayData = mergeEmployeesWithAttendance(employees, attendances, { start: today, end: today });
    
    const totalEmployees = employees.length; // Total employees in database
    
    // Count unique employees for today's statistics
    const uniqueEmployeesToday = new Set();
    const presentToday = new Set();
    const absentToday = new Set();
    const lateToday = new Set();
    const remoteToday = new Set();
    
    todayData.forEach(record => {
      uniqueEmployeesToday.add(record.employeeId);
      
      if (record.status === AttendanceStatus.PRESENT || record.status === AttendanceStatus.WORK_FROM_HOME) {
        presentToday.add(record.employeeId);
      }
      if (record.status === AttendanceStatus.ABSENT || record.status === AttendanceStatus.LEAVE) {
        absentToday.add(record.employeeId);
      }
      if (record.status === AttendanceStatus.LATE) {
        lateToday.add(record.employeeId);
      }
      if (record.isRemote) {
        remoteToday.add(record.employeeId);
      }
    });
    
    const present = presentToday.size;
    const absent = absentToday.size;
    const late = lateToday.size;
    const wfhCount = remoteToday.size;
    
    // Calculate work from home percentage
    const wfhPercentage = present > 0 ? Math.round((wfhCount / present) * 100) : 0;
    
    // Calculate average working hours for today only
    const todayWorkingHours = todayData
      .filter(a => presentToday.has(a.employeeId))
      .reduce((sum, a) => sum + (a.workHours || 0), 0);
    const avgWorkHours = present > 0 ? (todayWorkingHours / present).toFixed(1) : "0";
    
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 mb-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-blue-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Total Employees</p>
              <div className="flex items-baseline mt-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalEmployees}</p>
                <p className="ml-2 text-xs text-gray-500 dark:text-gray-400">tracked today</p>
              </div>
            </div>
            <div className="h-10 w-10 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
              <Users className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-3 text-xs font-medium text-gray-400 dark:text-gray-500">
            {new Date().toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-green-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Present Today</p>
              <div className="flex items-baseline mt-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{present}</p>
                <p className="ml-2 text-xs text-green-500 dark:text-green-400 font-medium">
                  {totalEmployees > 0 ? Math.round((present / totalEmployees) * 100) : 0}%
                </p>
              </div>
            </div>
            <div className="h-10 w-10 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
            </div>
          </div>
          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full mt-3">
            <div 
              className="h-2 bg-green-500 dark:bg-green-400 rounded-full transition-all duration-300" 
              style={{ width: `${totalEmployees > 0 ? (present / totalEmployees) * 100 : 0}%` }}
            ></div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-red-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Absent Today</p>
              <div className="flex items-baseline mt-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{absent}</p>
                <p className="ml-2 text-xs text-red-500 dark:text-red-400 font-medium">
                  {totalEmployees > 0 ? Math.round((absent / totalEmployees) * 100) : 0}%
                </p>
              </div>
            </div>
            <div className="h-10 w-10 rounded-full bg-red-50 dark:bg-red-900/30 flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
            </div>
          </div>
          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full mt-3">
            <div 
              className="h-2 bg-red-500 dark:bg-red-400 rounded-full transition-all duration-300" 
              style={{ width: `${totalEmployees > 0 ? (absent / totalEmployees) * 100 : 0}%` }}
            ></div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-yellow-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Late Arrivals</p>
              <div className="flex items-baseline mt-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{late}</p>
                <p className="ml-2 text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                  {totalEmployees > 0 ? Math.round((late / totalEmployees) * 100) : 0}%
                </p>
              </div>
            </div>
            <div className="h-10 w-10 rounded-full bg-yellow-50 dark:bg-yellow-900/30 flex items-center justify-center">
              <Clock className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
            </div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-3 flex items-center">
            <Clock className="h-3 w-3 mr-1" /> 
            {(() => {
              // Calculate average delay from late arrivals
              const lateArrivals = todayData.filter(a => a.status === AttendanceStatus.LATE);
              if (lateArrivals.length === 0) return "No delays today";
              
              const totalDelayMinutes = lateArrivals.reduce((sum, a) => {
                // Calculate delay in minutes if checkIn and scheduled start time are available
                if (a.checkIn && a.scheduledStart) {
                  const checkInTime = new Date(`${today}T${a.checkIn}`);
                  const scheduledTime = new Date(`${today}T${a.scheduledStart}`);
                  const delayMs = checkInTime.getTime() - scheduledTime.getTime();
                  return sum + Math.max(0, Math.floor(delayMs / (1000 * 60)));
                }
                return sum;
              }, 0);
              
              const avgDelay = Math.round(totalDelayMinutes / lateArrivals.length);
              return avgDelay > 0 ? `Avg. delay: ${avgDelay} min` : "Delays calculated";
            })()}
          </p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-indigo-500 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Working Remote</p>
              <div className="flex items-baseline mt-2">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{wfhCount}</p>
                <p className="ml-2 text-xs text-indigo-500 dark:text-indigo-400 font-medium">
                  {wfhPercentage}% of present
                </p>
              </div>
            </div>
            <div className="h-10 w-10 rounded-full bg-indigo-50 dark:bg-indigo-900/30 flex items-center justify-center">
              <Home className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
            </div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-3 flex items-center">
            <Clock className="h-3 w-3 mr-1" /> Avg. hours: {avgWorkHours}
          </p>
        </div>
      </div>
    );
  };

  // Conditionally render the main content or advanced time features
  if (showAdvancedFeatures) {
    // Get current employee data from the first attendance record or use default
    const currentEmployeeData = {
      id: Array.isArray(attendances) && attendances.length > 0 
        ? attendances[0].employeeId 
        : (employees.length > 0 ? employees[0].id : null),
      name: Array.isArray(attendances) && attendances.length > 0 
        ? attendances[0].employeeName 
        : (employees.length > 0 ? employees[0].name : "Unknown Employee"),
      department: Array.isArray(attendances) && attendances.length > 0 
        ? attendances[0].department 
        : (employees.length > 0 ? employees[0].department : "Unknown Department"),
      position: Array.isArray(attendances) && attendances.length > 0 
        ? attendances[0].position 
        : (employees.length > 0 ? employees[0].position : "Unknown Position")
    };
    
    return <AdvancedTimeAttendanceWrapper 
      onReturnToStandard={toggleAdvancedFeatures}
      employeeData={currentEmployeeData}
      employeeId={currentEmployeeData.id}
      employeeName={currentEmployeeData.name}
    />;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
        {/* Conditionally render statistics cards - hide when Mark Attendance tab is active */}
        {!tabsToHideStats.includes(activeView) && renderStatCards()}
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 mb-3">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-lg bg-blue-600 dark:bg-blue-500 flex items-center justify-center mr-4">
                <CalendarClock className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activeView === 3 ? 'Attendance Reports & Analytics' : 'Attendance Management'}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {activeView === 3 
                    ? 'Comprehensive attendance tracking, reporting, and data management system'
                    : 'Import and manage external attendance data from various sources'
                  }
                </p>
              </div>
            </div>
             
            <div className="flex items-center space-x-2 mt-4 md:mt-0">
              {/* Show action buttons only on Daily Attendance tab (index 0) */}
              {activeView === 0 && (
                <>
                  <button 
                    onClick={() => {
                      console.log('Import button clicked');
                      setShowImport(true);
                    }} 
                    className="flex items-center py-2 px-4 bg-green-600 dark:bg-green-500 text-white rounded-lg text-sm font-medium hover:bg-green-700 dark:hover:bg-green-600 transition-colors duration-200"
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Import Data
                  </button>
                  
                  <button 
                    onClick={() => setShowExportModal(true)}
                    className="flex items-center py-2 px-4 bg-blue-600 dark:bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </button>
                  
                  <button 
                    onClick={handleClearImportedData}
                    className="flex items-center py-2 px-4 bg-orange-600 dark:bg-orange-500 text-white rounded-lg text-sm font-medium hover:bg-orange-700 dark:hover:bg-orange-600 transition-colors duration-200"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Clear Imported
                  </button>
                  
                  <button 
                    onClick={handleClearAllData}
                    className="flex items-center py-2 px-4 bg-red-600 dark:bg-red-500 text-white rounded-lg text-sm font-medium hover:bg-red-700 dark:hover:bg-red-600 transition-colors duration-200"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear All
                  </button>
                  
                  <button 
                    onClick={refreshAttendanceData}
                    className="flex items-center py-2 px-4 bg-blue-600 dark:bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* Import Modal - Uncomment this after testing the simple modal */}
        {showImport && (
          <Portal>
            <AttendanceImport 
              onImport={handleImportAttendance}
              onClose={() => {
                console.log('Closing import modal');
                setShowImport(false);
              }}
            />
          </Portal>
        )}
        
        <Tab.Group 
          selectedIndex={activeView} 
          onChange={setActiveView}
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <Tab.List className="flex flex-wrap md:flex-nowrap border-b border-gray-200 dark:border-gray-700 px-1">
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <ClipboardList className="h-3 w-3 mr-1" />
                Daily Attendance
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <Clock className="h-3 w-3 mr-1" />
                Mark Attendance
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <CalendarIcon className="h-3 w-3 mr-1" />
                Calendar View
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <BarChart3 className="h-3 w-3 mr-1" />
                Reports
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <UserCog className="h-3 w-3 mr-1" />
                Regularization
                {regularizationRequests.length > 0 && (
                  <span className="ml-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-xs rounded-full px-1.5 py-0.5 text-[10px]">
                    {regularizationRequests.length}
                  </span>
                )}
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <Building2 className="h-3 w-3 mr-1" />
                Shifts
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <Users className="h-3 w-3 mr-1" />
                Team Calendar
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <CalendarDays className="h-3 w-3 mr-1" />
                Holidays
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <UserCircle className="h-3 w-3 mr-1" />
                Employee Records
              </Tab>
              <Tab className={({ selected }) =>
                `py-1.5 px-2 text-xs font-medium border-b-2 transition-colors flex items-center whitespace-nowrap
                ${selected 
                  ? 'border-purple-600 dark:border-purple-400 text-purple-600 dark:text-purple-400'
                  : 'border-transparent text-purple-500 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:border-gray-300 dark:hover:border-gray-600'}`
              }>
                <Clock className="h-3 w-3 mr-1" />
                Advanced Time
              </Tab>
            </Tab.List>
            
            <Tab.Panels>
              <Tab.Panel>
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="relative">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-4">Loading attendance data...</div>
                    </div>
                  </div>
                ) : error ? (
                  <div className="flex items-center justify-center h-64 text-red-500 p-4">
                    <div className="text-center">
                      <AlertCircle className="h-10 w-10 mx-auto mb-2 text-red-500" />
                      <p className="text-red-500 font-medium">{error}</p>
                      <button className="mt-2 text-sm text-blue-600 hover:text-blue-800" onClick={() => window.location.reload()}>
                        Retry
                      </button>
                    </div>
                  </div>
                ) : employees.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 p-4">
                    <FileSpreadsheet className="h-16 w-16 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700">No Employees Found</h3>
                    <p className="text-sm text-gray-500 text-center max-w-md mt-2">
                      No employees found in the database. Please add employees first before importing attendance data.
                    </p>
                    <div className="flex space-x-3 mt-4">
                    <button 
                      onClick={() => setShowImport(true)} 
                        className="flex items-center py-3 px-6 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 shadow-md"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                        Import Attendance Data
                      </button>
                      <button 
                        onClick={refreshAttendanceData}
                        className="flex items-center py-3 px-6 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 shadow-md"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh from Database
                    </button>
                    </div>
                    <p className="text-xs text-gray-400 mt-3 text-center">
                      Professional attendance system with strict employee validation
                    </p>
                  </div>
                ) : (
                  <>
                    <AttendanceTable 
                      attendances={mergedAttendanceData}
                      onUpdateAttendance={handleUpdateAttendance}
                      onDeleteAttendance={handleDeleteAttendance}
                      selectedDate={selectedDate}
                      onDateChange={handleDateChange}
                      onDateRangeChange={handleDateRangeChange}
                      datePeriod={datePeriod}
                      onDatePeriodChange={handleDatePeriodChange}
                      onEmployeeClick={handleSelfServiceOpen}
                      onFiltersUpdate={handleFiltersUpdate}
                      shifts={shifts}
                    />
                  </>
                )}
              </Tab.Panel>
              
              <Tab.Panel>
                <AttendanceForm 
                  onSubmit={handleAddAttendance} 
                  employees={employees}
                  shifts={shifts}
                />
              </Tab.Panel>
              
              <Tab.Panel>
                <AttendanceCalendar 
                  attendances={allAttendances}
                  selectedDate={selectedDate}
                  onDateChange={handleDateChange}
                  holidays={holidays}
                  weekendDays={weekendDays}
                />
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="p-4">
                  <div className="mb-6">
                    <AttendanceReport />
                  </div>
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="p-6">
                  <RegularizationManager
                    mode="table"
                    showFormButton={true}
                    requests={regularizationRequests}
                    employeeId={0} // Not used in admin mode
                    employeeName="" // Not used in admin mode
                    onSubmit={handleHRRegularizationSubmit}
                    onCancel={() => setShowHRRegularizationForm(false)}
                    onApprove={handleApproveRegularization}
                    onReject={handleRejectRegularization}
                    onEdit={handleViewRegularizationDetails}
                    isSubmitting={false}
                    compact={false}
                    isAdmin={true}
                    showFilters={true}
                    showStats={true}
                    employees={employees}
                  />
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Shift Management</h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Configure and manage employee work schedules and shifts</p>
                    </div>
                    <div className="flex gap-3">
                      <button 
                        onClick={refreshShifts}
                        className="flex items-center py-2 px-4 bg-gray-600 dark:bg-gray-500 text-white rounded-lg text-sm font-medium hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200"
                        disabled={loading}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                      </button>
                      <button 
                        onClick={() => setShowShiftAssignment(true)}
                        className="flex items-center py-2 px-4 bg-green-600 dark:bg-green-500 text-white rounded-lg text-sm font-medium hover:bg-green-700 dark:hover:bg-green-600 transition-colors duration-200"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Assign Shifts
                      </button>
                      <button 
                        onClick={() => {
                          setShiftToEdit(undefined);
                          setShowShiftForm(true);
                        }}
                        className="flex items-center py-2 px-4 bg-blue-600 dark:bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        Create New Shift
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
                    <div className="border-b border-gray-200 dark:border-gray-700 px-4 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Active Shifts</h3>
                      <div className="flex items-center">
                        <button className="text-xs text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex items-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
                          <Filter className="h-3 w-3 mr-1" />
                          Filter
                        </button>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Start Time</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">End Time</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Working Hours</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Break Duration</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Working Days</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {shifts.map(shift => (
                            <tr key={shift.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div 
                                    className="h-3 w-3 rounded-full mr-2" 
                                    style={{ backgroundColor: shift.color }}
                                  ></div>
                                  <div className="text-sm font-medium text-gray-900 dark:text-white">{shift.name}</div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {new Date(`2000-01-01T${shift.startTime}`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {new Date(`2000-01-01T${shift.endTime}`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{shift.requiredWorkHours} hours</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{shift.breakDuration} minutes</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {shift.workingDays.map(day => {
                                  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                  return dayNames[day];
                                }).join(', ')}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  shift.isActive 
                                    ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' 
                                    : 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-400'
                                }`}>
                                  {shift.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-right space-x-2">
                                <button 
                                  onClick={() => handleEditShift(shift)}
                                  className="px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
                                >
                                  Edit
                                </button>
                                <button 
                                  onClick={() => handleToggleShiftStatus(shift.id)}
                                  className={`px-2 py-1 ${
                                    shift.isActive 
                                      ? 'bg-gray-50 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-900/50' 
                                      : 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-900/50'
                                  } rounded border transition-colors duration-200`}
                                >
                                  {shift.isActive ? 'Deactivate' : 'Activate'}
                                </button>
                                <button 
                                  onClick={() => handleDeleteShift(shift.id)}
                                  className="px-2 py-1 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded border border-red-200 dark:border-red-700 hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors duration-200"
                                >
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Shift Assignments</h3>
                    
                    {shiftAssignments.length === 0 ? (
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
                        <p className="text-gray-500 dark:text-gray-400">No shift assignments found</p>
                        <button 
                          onClick={() => setShowShiftAssignment(true)}
                          className="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                        >
                          <Users className="h-4 w-4 mr-2" />
                          Assign Shifts
                        </button>
                      </div>
                    ) : (
                      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-lg dark:shadow-gray-900/20">
                        {/* Search and Filter Header */}
                        <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-3">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                  Shift Assignments
                                </h3>
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                  {shiftAssignments.length} total
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <label className="text-sm font-medium text-gray-600 dark:text-gray-300">Show:</label>
                                <select
                                  value={itemsPerPage}
                                  onChange={(e) => {
                                    setItemsPerPage(parseInt(e.target.value));
                                    setCurrentPage(1); // Reset to first page when changing page size
                                  }}
                                  className="border border-gray-300 dark:border-gray-600 rounded-lg text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 dark:text-white shadow-sm"
                                >
                                  <option value={5}>5</option>
                                  <option value={10}>10</option>
                                  <option value={25}>25</option>
                                  <option value={50}>50</option>
                                  <option value={100}>100</option>
                                </select>
                                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">per page</span>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-3">
                              <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                  <Search className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                                </div>
                                <input
                                  type="text"
                                  value={searchAssignments}
                                  onChange={(e) => {
                                    setSearchAssignments(e.target.value);
                                    setCurrentPage(1); // Reset to first page when searching
                                  }}
                                  placeholder="Search assignments..."
                                  className="block w-64 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 shadow-sm"
                                />
                              </div>
                              <button 
                                onClick={() => setShowShiftAssignment(true)}
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 dark:from-blue-500 dark:to-indigo-500 dark:hover:from-blue-600 dark:hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
                              >
                                <Users className="h-4 w-4 mr-2" />
                                Assign More
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead className="bg-gray-50 dark:bg-gray-800">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Employee</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Shift</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Assignment Type</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Start Date</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">End Date</th>
                                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                              {(() => {
                                // Filter assignments based on search
                                const filteredAssignments = shiftAssignments.filter(assignment => {
                                  const employee = employees.find(emp => emp.id === assignment.employeeId);
                                  const employeeName = employee ? `${employee.firstName || ''} ${employee.lastName || ''}`.trim() : '';
                                  const shift = shifts.find(s => s.id === assignment.shiftId);
                                  const shiftName = shift?.name || '';
                                  
                                  const searchTerm = searchAssignments.toLowerCase();
                                  return employeeName.toLowerCase().includes(searchTerm) ||
                                         shiftName.toLowerCase().includes(searchTerm) ||
                                         (employee?.department || '').toLowerCase().includes(searchTerm);
                                });
                                
                                // Pagination calculation
                                const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);
                                const startIndex = (currentPage - 1) * itemsPerPage;
                                const paginatedAssignments = filteredAssignments.slice(startIndex, startIndex + itemsPerPage);
                                
                                return paginatedAssignments.map((assignment, index) => {
                                  const shift = shifts.find(s => s.id === assignment.shiftId);
                                  // Find the actual employee from the employees array
                                  const employee = employees.find(emp => emp.id === assignment.employeeId);
                                  const employeeName = employee ? `${employee.firstName || ''} ${employee.lastName || ''}`.trim() : `Employee ID: ${assignment.employeeId}`;
                                  const employeeDepartment = employee?.department || '';
                                  
                                  return (
                                    <tr key={`${assignment.employeeId}-${assignment.shiftId}-${startIndex + index}`} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <div className="flex items-center">
                                          <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-sm mr-3">
                                            {employee?.profileImagePath ? (
                                              <img 
                                                src={employee.profileImagePath} 
                                                alt={employeeName} 
                                                className="h-8 w-8 rounded-full object-cover" 
                                              />
                                            ) : (
                                              employeeName.split(' ').map(n => n[0]).join('').substring(0, 2)
                                            )}
                                          </div>
                                          <div>
                                            <div className="text-sm font-medium text-gray-900 dark:text-white">{employeeName}</div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">{employeeDepartment}</div>
                                          </div>
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                          {shift && (
                                            <>
                                              <div 
                                                className="h-3 w-3 rounded-full mr-2" 
                                                style={{ backgroundColor: shift.color }}
                                              ></div>
                                              <div className="text-sm font-medium text-gray-900 dark:text-white">{shift.name}</div>
                                            </>
                                          )}
                                        </div>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                          (!assignment.endDate && assignment.isPermanent)
                                            ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' 
                                            : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                                        }`}>
                                          {(!assignment.endDate && assignment.isPermanent) ? 'Permanent' : 'Temporary'}
                                        </span>
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {new Date(assignment.startDate).toLocaleDateString()}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {(() => {
                                          // Temporary fix: Determine if assignment is truly permanent
                                          // If it has an endDate, it should be temporary regardless of isPermanent flag
                                          const isTrulyPermanent = !assignment.endDate && assignment.isPermanent;
                                          
                                          if (isTrulyPermanent) {
                                            return (
                                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                Permanent
                                              </span>
                                            );
                                          } else if (assignment.endDate) {
                                            return new Date(assignment.endDate).toLocaleDateString();
                                          } else {
                                            return (
                                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                No End Date
                                              </span>
                                            );
                                          }
                                        })()}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                                        <div className="flex justify-end space-x-2">
                                          <button 
                                            onClick={() => handleEditAssignment(assignment)}
                                            className="px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/50"
                                          >
                                            Edit
                                          </button>
                                          <button 
                                            onClick={() => {
                                              // Find the correct index in the original array
                                              const originalIndex = shiftAssignments.findIndex(a => 
                                                a.employeeId === assignment.employeeId && 
                                                a.shiftId === assignment.shiftId &&
                                                a.startDate === assignment.startDate
                                              );
                                              if (originalIndex !== -1) {
                                                removeShiftAssignment(originalIndex);
                                              }
                                            }}
                                            className="px-2 py-1 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded border border-red-200 dark:border-red-700 hover:bg-red-100 dark:hover:bg-red-900/50"
                                          >
                                            Remove
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                });
                              })()}
                            </tbody>
                          </table>
                        </div>
                        
                        {/* Pagination Controls */}
                        {(() => {
                          const filteredAssignments = shiftAssignments.filter(assignment => {
                            const employee = employees.find(emp => emp.id === assignment.employeeId);
                            const employeeName = employee ? `${employee.firstName || ''} ${employee.lastName || ''}`.trim() : '';
                            const shift = shifts.find(s => s.id === assignment.shiftId);
                            const shiftName = shift?.name || '';
                            
                            const searchTerm = searchAssignments.toLowerCase();
                            return employeeName.toLowerCase().includes(searchTerm) ||
                                   shiftName.toLowerCase().includes(searchTerm) ||
                                   (employee?.department || '').toLowerCase().includes(searchTerm);
                          });
                          
                          const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);
                          const startIndex = (currentPage - 1) * itemsPerPage;
                          const endIndex = Math.min(startIndex + itemsPerPage, filteredAssignments.length);
                          
                          if (totalPages > 1) {
                            return (
                              <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                <div className="flex items-center space-x-4">
                                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Showing <span className="font-semibold text-gray-900 dark:text-white">{startIndex + 1}</span> to <span className="font-semibold text-gray-900 dark:text-white">{endIndex}</span> of{' '}
                                    <span className="font-semibold text-gray-900 dark:text-white">{filteredAssignments.length}</span> assignments
                                  </p>
                                  {searchAssignments && (
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                      Filtered
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  <button 
                                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1}
                                    className="relative inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                                  >
                                    Previous
                                  </button>
                                  
                                  {/* Page numbers */}
                                  <div className="flex items-center space-x-1">
                                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                                      let page;
                                      if (totalPages <= 5) {
                                        page = i + 1;
                                      } else if (currentPage <= 3) {
                                        page = i + 1;
                                      } else if (currentPage >= totalPages - 2) {
                                        page = totalPages - 4 + i;
                                      } else {
                                        page = currentPage - 2 + i;
                                      }
                                      
                                      return (
                                        <button
                                          key={page}
                                          onClick={() => setCurrentPage(page)}
                                          className={`relative inline-flex items-center px-3 py-2 border text-sm font-medium rounded-lg transition-all duration-200 ${
                                            currentPage === page
                                              ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 ring-2 ring-blue-500 dark:ring-blue-400 ring-opacity-25'
                                              : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800'
                                          }`}
                                        >
                                          {page}
                                        </button>
                                      );
                                    })}
                                  </div>
                                  
                                  <button 
                                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                    disabled={currentPage === totalPages}
                                    className="relative inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                                  >
                                    Next
                                  </button>
                                </div>
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    )}
                  </div>
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="mt-4 overflow-x-auto" style={{ maxWidth: '100%' }}>
                  <TeamCalendar 
                    attendances={allAttendances}
                    shifts={shifts}
                    employees={employees}
                    holidays={holidays}
                    weekendDays={weekendDays}
                    shiftAssignments={shiftAssignments}
                    onDateChange={handleDateChange}
                  />
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="mt-4">
                  <HolidayConfiguration 
                    initialHolidays={holidays}
                    initialWeekendDays={weekendDays}
                    onSave={handleSaveHolidayConfig}
                  />
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="p-4">
                  <div className="mb-6 flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Employee Information Center</h3>
                      <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-4">
                        Access detailed employee records, attendance history, and manage employee information.
                      </p>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="text-center py-10">
                      <UserCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Employee Information Center</h3>
                      <p className="text-gray-500 max-w-md mx-auto mb-4">
                        Access detailed employee records, attendance history, and manage employee information.
                      </p>
                      
                      <div className="mt-6">
                        {Array.isArray(allAttendances) && allAttendances.length > 0 ? (
                          // Show a small list of employees to choose from
                          <div>
                            <p className="text-sm text-gray-500 mb-2">Select an employee to view their detailed information:</p>
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                              {Array.from(new Set((Array.isArray(allAttendances) ? allAttendances : []).map((a: Attendance) => a.employeeId))).slice(0, 6).map(empId => {
                                const emp = (Array.isArray(allAttendances) ? allAttendances : []).find((a: Attendance) => a.employeeId === empId);
                                const employeeData = employees.find(e => e.id === empId);
                                return (
                                  <div 
                                    key={empId}
                                    onClick={() => handleSelfServiceOpen(empId)} 
                                    className="border border-gray-200 rounded-lg p-4 flex items-center hover:bg-blue-50 hover:border-blue-200 cursor-pointer"
                                  >
                                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-sm mr-3 overflow-hidden">
                                      {employeeData?.profileImagePath ? (
                                        <img 
                                          src={employeeData.profileImagePath} 
                                          alt={emp?.employeeName} 
                                          className="h-full w-full object-cover rounded-full" 
                                        />
                                      ) : (
                                        emp?.employeeName.split(' ').map(n => n[0]).join('')
                                      )}
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium text-gray-900">{emp?.employeeName}</div>
                                      <div className="text-xs text-gray-500">{employeeData?.department || emp?.department}</div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <p className="text-gray-500">No employee data available. Import attendance data first.</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Tab.Panel>
              
              <Tab.Panel>
                <div className="p-4">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Advanced Time & Attendance</h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Access biometric verification, overtime calculations, and shift differential management</p>
                    </div>
                    <button 
                      onClick={toggleAdvancedFeatures}
                      className="flex items-center py-2 px-4 bg-purple-600 dark:bg-purple-500 text-white rounded-lg text-sm font-medium hover:bg-purple-700 dark:hover:bg-purple-600 transition-colors duration-200"
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      Open Advanced Features
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-purple-500 dark:border-purple-400">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                        <Fingerprint className="h-5 w-5 mr-2 text-purple-500 dark:text-purple-400" />
                        Biometric Clock-In
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Use facial recognition or fingerprint verification for secure attendance tracking.
                      </p>
                      <button 
                        onClick={toggleAdvancedFeatures}
                        className="text-sm text-purple-600 dark:text-purple-400 font-medium hover:text-purple-700 dark:hover:text-purple-300 transition-colors duration-200"
                      >
                        Access Feature →
                      </button>
                    </div>
                    
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500 dark:border-blue-400">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                        <CircleDollarSign className="h-5 w-5 mr-2 text-blue-500 dark:text-blue-400" />
                        Overtime Calculator
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Calculate overtime with different rates for regular, double, holiday, and weekend hours.
                      </p>
                      <button 
                        onClick={toggleAdvancedFeatures}
                        className="text-sm text-purple-600 dark:text-purple-400 font-medium hover:text-purple-700 dark:hover:text-purple-300 transition-colors duration-200"
                      >
                        Access Feature →
                      </button>
                    </div>
                    
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-cyan-500 dark:border-cyan-400">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                        <Moon className="h-5 w-5 mr-2 text-cyan-500 dark:text-cyan-400" />
                        Shift Differentials
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Manage premium pay rates for different shift types and calculate effective hourly rates.
                      </p>
                      <button 
                        onClick={toggleAdvancedFeatures}
                        className="text-sm text-purple-600 dark:text-purple-400 font-medium hover:text-purple-700 dark:hover:text-purple-300 transition-colors duration-200"
                      >
                        Access Feature →
                      </button>
                    </div>
                  </div>
                </div>
              </Tab.Panel>
            </Tab.Panels>
          </div>
        </Tab.Group>
 
        {/* Shift Form Modal */}
        {showShiftForm && (
          <ShiftForm 
            onSubmit={shiftToEdit ? handleUpdateShift : handleAddShift}
            onCancel={() => {
              setShowShiftForm(false);
              setShiftToEdit(undefined);
            }}
            shiftToEdit={shiftToEdit}
          />
        )}
        
        {/* Shift Assignment Modal */}
        {showShiftAssignment && (
          <ShiftAssignment
            shifts={shifts.filter(s => s.isActive)}
            onCancel={() => {
              setShowShiftAssignment(false);
              setAssignmentToEdit(undefined);
            }}
            onAssign={handleAssignShifts}
            assignmentToEdit={assignmentToEdit}
            employees={employees}
          />
        )}
 
                        {/* Employee Portal Modal */}
        {showSelfService && selectedEmployeeId && (
          <EmployeeAttendanceModal
            employeeId={selectedEmployeeId}
            employeeName={(() => {
              const employee = employees.find(emp => emp.id === selectedEmployeeId);
              if (employee) {
                return `${employee.firstName || ''} ${employee.lastName || ''}`.trim();
              }
              // Fallback to attendance record if employee not found in database
              return (Array.isArray(allAttendances) ? allAttendances : []).find(a => a.employeeId === selectedEmployeeId)?.employeeName || 'Employee';
            })()}
            employeeCode={(() => {
              const employee = employees.find(emp => emp.id === selectedEmployeeId);
              return employee?.employeeId || `EMP-${selectedEmployeeId}`;
            })()}
            employeeDepartment={(() => {
              const employee = employees.find(emp => emp.id === selectedEmployeeId);
              return employee?.department || undefined;
            })()}
            employeePosition={(() => {
              const employee = employees.find(emp => emp.id === selectedEmployeeId);
              return employee?.designation || undefined;
            })()}
            employeePhoto={(() => {
              const employee = employees.find(emp => emp.id === selectedEmployeeId);
              console.log('Employee photo resolution:', {
                employeeFound: !!employee,
                employeeName: employee?.firstName + ' ' + employee?.lastName,
                profileImagePath: employee?.profileImagePath,
                rawEmployee: employee
              });
              return employee?.profileImagePath;
            })()}
            attendances={Array.isArray(allAttendances) ? allAttendances : []}
            onSubmitRegularization={handleSubmitRegularization}
            onClose={handleSelfServiceClose}
          />
        )}

    
      </div>

      {/* Enhanced Export Modal */}
      <AttendanceExport
        attendances={allAttendances}
        onClose={() => setShowExportModal(false)}
        isOpen={showExportModal}
        currentFilters={{
          dateRange,
          datePeriod,
          selectedDepartment: '', // You can pass actual selected department if you have it
          selectedStatus: '',     // You can pass actual selected status if you have it
          searchQuery: ''         // You can pass actual search query if you have it
        }}
        currentViewData={mergedAttendanceData}
      />

      {/* HR/Admin Regularization Form Modal */}
      {showHRRegularizationForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-lg relative">
            <button
              onClick={() => { setShowHRRegularizationForm(false); setSelectedEmployeeForReg(null); }}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-xl font-bold"
              aria-label="Close"
            >
              ×
            </button>
            <div className="mb-4">
              <label htmlFor="hr-employee-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Employee</label>
              <select
                id="hr-employee-select"
                value={selectedEmployeeForReg ?? ''}
                onChange={e => setSelectedEmployeeForReg(e.target.value === '' ? null : e.target.value)}
                className="block w-full border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="" disabled>Select an employee...</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name} ({emp.employeeId || emp.id})</option>
                ))}
              </select>
            </div>
            {selectedEmployeeForReg && (
              <RegularizationManager
                mode="form"
                showFormButton={false}
                requests={regularizationRequests}
                employeeId={selectedEmployeeForReg ? parseInt(selectedEmployeeForReg, 10) : 0}
                employeeName={selectedEmployeeForReg ? (employees.find(emp => emp.id === parseInt(selectedEmployeeForReg, 10))?.name || '') : ''}
                onSubmit={handleHRRegularizationSubmit}
                onCancel={() => setShowHRRegularizationForm(false)}
                isSubmitting={false}
                compact={false}
              />
            )}
          </div>
        </div>
      )}
      
    </div>
  );
};

export default AttendanceManagement; 