import React from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer, Cell
} from 'recharts';
import { Card, CardHeader, CardContent, Typography } from '@mui/material';
import { Calendar, Clock, TrendingUp } from 'lucide-react';

interface AttendanceData {
  date: string;
  present: number;
  absent: number;
  late: number;
  onLeave: number;
}

interface AttendanceOverviewChartProps {
  data: AttendanceData[];
  title?: string;
  height?: number;
}

const COLORS = {
  present: '#4CAF50',
  late: '#FFC107',
  onLeave: '#2196F3',
  absent: '#F44336'
};

const AttendanceOverviewChart: React.FC<AttendanceOverviewChartProps> = ({
  data = [],
  title = 'Monthly Attendance Overview',
  height = 350
}) => {
  // Calculate summary metrics
  const summary = data.reduce(
    (acc, day) => {
      acc.totalPresent += day.present || 0;
      acc.totalLate += day.late || 0;
      acc.totalOnLeave += day.onLeave || 0;
      acc.totalAbsent += day.absent || 0;
      return acc;
    },
    { totalPresent: 0, totalLate: 0, totalOnLeave: 0, totalAbsent: 0 }
  );

  const totalEmployees = Math.max(
    summary.totalPresent + summary.totalAbsent + summary.totalLate + summary.totalOnLeave,
    1
  );

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader
        title={
          <div className="flex items-center justify-between">
            <Typography variant="h6" className="font-semibold text-gray-800 dark:text-white">
              {title}
            </Typography>
            <div className="flex items-center space-x-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-gray-500">
                {new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
              </span>
            </div>
          </div>
        }
        className="pb-0"
      />
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <StatCard 
            title="Present" 
            value={summary.totalPresent} 
            percentage={(summary.totalPresent / totalEmployees * 100).toFixed(1) + '%'} 
            color="green" 
            icon={<CheckCircle className="h-5 w-5" />} 
          />
          <StatCard 
            title="Late" 
            value={summary.totalLate} 
            percentage={(summary.totalLate / totalEmployees * 100).toFixed(1) + '%'} 
            color="yellow" 
            icon={<Clock className="h-5 w-5" />} 
          />
          <StatCard 
            title="On Leave" 
            value={summary.totalOnLeave} 
            percentage={(summary.totalOnLeave / totalEmployees * 100).toFixed(1) + '%'} 
            color="blue" 
            icon={<Calendar className="h-5 w-5" />} 
          />
          <StatCard 
            title="Absent" 
            value={summary.totalAbsent} 
            percentage={(summary.totalAbsent / totalEmployees * 100).toFixed(1) + '%'} 
            color="red" 
            icon={<XCircle className="h-5 w-5" />} 
          />
        </div>
        
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer>
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
              barGap={0}
              barCategoryGap="10%"
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return date.toLocaleDateString('en-US', { day: 'numeric', month: 'short' });
                }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => (value > 1000 ? `${value / 1000}k` : value)}
              />
              <Tooltip 
                formatter={(value, name) => [`${value} employees`, name.replace(/([A-Z])/g, ' $1').trim()]}
                labelFormatter={(label) => {
                  const date = new Date(label);
                  return date.toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    month: 'short', 
                    day: 'numeric' 
                  });
                }}
              />
              <Legend 
                iconType="circle" 
                iconSize={10}
                formatter={(value) => (
                  <span className="text-xs capitalize">
                    {value.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                )}
              />
              <Bar dataKey="present" name="Present" fill={COLORS.present} radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-present-${index}`} fill={COLORS.present} />
                ))}
              </Bar>
              <Bar dataKey="late" name="Late" fill={COLORS.late} radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-late-${index}`} fill={COLORS.late} />
                ))}
              </Bar>
              <Bar dataKey="onLeave" name="On Leave" fill={COLORS.onLeave} radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-onLeave-${index}`} fill={COLORS.onLeave} />
                ))}
              </Bar>
              <Bar dataKey="absent" name="Absent" fill={COLORS.absent} radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-absent-${index}`} fill={COLORS.absent} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1" />
            <span>Monthly average: {Math.round(summary.totalPresent / Math.max(1, data.length))} present per day</span>
          </div>
          <div className="text-right">
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// StatCard component for displaying metrics
interface StatCardProps {
  title: string;
  value: number | string;
  percentage: string;
  color: 'green' | 'yellow' | 'blue' | 'red';
  icon: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, percentage, color, icon }) => {
  const colorClasses = {
    green: 'bg-green-50 text-green-600 dark:bg-green-900/30 dark:text-green-400',
    yellow: 'bg-yellow-50 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400',
    blue: 'bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
    red: 'bg-red-50 text-red-600 dark:bg-red-900/30 dark:text-red-400',
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-500 dark:text-gray-400">{title}</p>
          <p className="text-lg font-bold text-gray-900 dark:text-white">{value}</p>
          <p className={`text-xs font-medium ${colorClasses[color]}`}>
            {percentage} of total
          </p>
        </div>
        <div className={`p-2 rounded-full ${colorClasses[color].split(' ')[0]} bg-opacity-20`}>
          {React.cloneElement(icon as React.ReactElement, { className: 'h-5 w-5' })}
        </div>
      </div>
    </div>
  );
};

export default AttendanceOverviewChart;
