import { MigrationInterface, QueryRunner } from "typeorm";

export class FixTicketSchema1740333100000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, backup the visibleTo data
        const tickets = await queryRunner.query(
            `SELECT id, ticketNumber, visibleTo FROM tickets`
        );

        // Modify the column type to JSON
        await queryRunner.query(
            `ALTER TABLE tickets MODIFY COLUMN visibleTo JSON`
        );

        // Update each ticket's visibility to ensure proper JSON format
        for (const ticket of tickets) {
            try {
                let visibleTo = ticket.visibleTo;
                
                // If it's a string, try to parse it
                if (typeof visibleTo === 'string') {
                    try {
                        visibleTo = JSON.parse(visibleTo);
                    } catch {
                        // If parsing fails, assume it's a comma-separated string
                        visibleTo = visibleTo.split(',').filter(Boolean);
                    }
                }

                // Ensure it's an array
                if (!Array.isArray(visibleTo)) {
                    visibleTo = [visibleTo].filter(Boolean);
                }

                await queryRunner.query(
                    `UPDATE tickets SET visibleTo = ? WHERE id = ?`,
                    [JSON.stringify(visibleTo), ticket.id]
                );

                console.log(`Updated ticket ${ticket.ticketNumber} visibility:`, visibleTo);
            } catch (error) {
                console.error(`Error updating ticket ${ticket.ticketNumber}:`, error);
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Convert back to text if needed
        await queryRunner.query(
            `ALTER TABLE tickets MODIFY COLUMN visibleTo TEXT`
        );
    }
} 