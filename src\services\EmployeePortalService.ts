import api from './api';
import { employeeApi } from './employeeApi';
import { leaveBalancesApi, leaveRequestsApi } from './leaveApi';

export interface EmployeePortalProfile {
  id: number;
  employeeId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  department: string;
  designation: string;
  joinDate: string;
  employmentType: string;
  reportingManager: string;
  location: string;
  phone: string;
  profileImagePath?: string;
  
  // Personal Information
  gender: string;
  dateOfBirth: string;
  religion?: string;
  cnicNumber: string;
  cnicExpiryDate?: string;
  nationality?: string;
  maritalStatus?: string;
  bloodType?: string;
  fatherName?: string;
  status: string;
  statusDate?: string;
  notes?: string;
  specialInstructions?: string;
  
  // Employment Details
  employmentStatus?: string;
  employeeLevel?: string;
  probationEndDate?: string;
  noticePeriod?: string;
  reportingTo?: string;
  remoteWorkEligible?: boolean;
  nextReviewDate?: string;
  trainingRequirements?: string;
  workSchedule?: string;
  shiftType?: string;
  project?: string;
  
  // Compensation & Benefits
  totalSalary?: string;
  salaryTier?: string;
  salaryTrench?: string;
  cashAmount?: string;
  bankAmount?: string;
  paymentMode?: string;
  
  // Allowances
  foodAllowanceInSalary?: boolean;
  fuelAllowanceInSalary?: boolean;
  numberOfMeals?: string;
  fuelInLiters?: string;
  fuelAmount?: string;
  foodProvidedByCompany?: boolean;
  
  // Bank Details
  bankName?: string;
  bankBranch?: string;
  accountNumber?: string;
  accountTitle?: string;
  iban?: string;
  
  // Insurance
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;
  
  // Accommodation
  accommodationProvidedByEmployer?: boolean;
  accommodationType?: string;
  accommodationAddress?: string;
  
  // Contact Information
  mobileNumber: string;
  officialNumber?: string;
  officialEmail?: string;
  personalEmail?: string;
  permanentAddress?: string;
  currentAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  linkedinProfile?: string;
  otherSocialProfiles?: string;
  
  // Education
  education: EmployeeEducationData[];
  
  // Experience
  experience: EmployeeExperienceData[];
  
  // Family
  family: EmployeeFamilyData[];
  
  // Skills
  skills: EmployeeSkillsData[];
  
  // Devices
  devices: EmployeeDeviceData[];
  
  // Projects
  projects: EmployeeProjectData[];
  
  // Vehicles
  vehicles: EmployeeVehicleData[];
  
  // Health Records
  healthRecords: EmployeeHealthData[];
}

export interface EmployeeEducationData {
  id: number;
  educationLevel: string;
  degree: string;
  major?: string;
  institution: string;
  graduationYear: string;
  grade?: string;
}

export interface EmployeeExperienceData {
  id: number;
  companyName: string;
  position: string;
  startDate: string;
  endDate?: string;
  description?: string;
  salary?: string;
  reasonForLeaving?: string;
}

export interface EmployeeFamilyData {
  id: number;
  name: string;
  dateOfBirth?: string;
  relationship: string;
  gender?: string;
  cnic?: string;
  occupation?: string;
  employer?: string;
  contactNumber?: string;
  type: string; // 'spouse', 'child', 'dependent'
}

export interface EmployeeSkillsData {
  id: number;
  professionalSkills?: string;
  technicalSkills?: string;
  certifications?: string;
  languages?: string;
}

export interface EmployeeDeviceData {
  id: number;
  deviceType: string;
  deviceBrand?: string;
  deviceModel?: string;
  serialNumber?: string;
  assignedDate: string;
  returnDate?: string;
  condition?: string;
  notes?: string;
}

export interface EmployeeProjectData {
  id: number;
  projectName: string;
  role: string;
  startDate: string;
  endDate?: string;
  description?: string;
  status: string;
}

export interface EmployeeVehicleData {
  id: number;
  vehicleType: string;
  make?: string;
  model?: string;
  year?: string;
  licensePlate?: string;
  assignedDate: string;
  returnDate?: string;
}

export interface EmployeeHealthData {
  id: number;
  recordType: string;
  recordDate: string;
  description?: string;
  doctorName?: string;
  hospitalName?: string;
  medication?: string;
  notes?: string;
}

export interface EmployeeLeaveData {
  balances: {
    leaveType: string;
    total: number;
    used: number;
    pending: number;
    remaining: number;
    carryForward?: number;
    expiryDate?: string;
  }[];
  recentRequests: {
    id: number;
    type: string;
    from: string;
    to: string;
    status: string;
    days: number;
    reason?: string;
  }[];
  pendingRequestsCount: number;
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

export interface EmployeePayrollData {
  lastPayment: {
    date: string;
    amount: string;
    currency: string;
  };
  ytdEarnings: string;
  payslips: {
    id: string;
    period: string;
    date: string;
    amount: string;
    status: string;
    downloadUrl?: string;
  }[];
  bankDetails?: {
    bankName: string;
    accountNumber: string;
  };
}

export interface EmployeePerformanceData {
  currentRating?: number;
  lastReviewDate?: string;
  nextReviewDate?: string;
  goals: {
    id: number;
    title: string;
    status: 'pending' | 'in_progress' | 'completed';
    progress: number;
    deadline?: string;
  }[];
  achievements: {
    id: number;
    title: string;
    description: string;
    date: string;
    type: 'award' | 'recognition' | 'milestone';
  }[];
}

export interface EmployeeDocumentData {
  documents: {
    id: number;
    name: string;
    type: string;
    uploadDate: string;
    size: string;
    downloadUrl: string;
  }[];
  totalCount: number;
}

export interface EmployeeAttendanceData {
  summary: {
    currentMonth: {
      totalDays: number;
      presentDays: number;
      absentDays: number;
      leaveDays: number;
      holidayDays: number;
      workingDays: number;
      punctualityScore: number;
    };
    recentAttendance: {
      date: string;
      checkIn: string;
      checkOut: string;
      status: 'present' | 'absent' | 'late' | 'early_out';
      totalHours: number;
    }[];
  };
  allAttendanceRecords?: any[]; // Full attendance records for enhanced view
}

export interface EmployeeBenefitsData {
  salary: {
    totalSalary: string;
    salaryTier: string;
    salaryTrench: string;
    cashAmount: string;
    bankAmount: string;
    paymentMode: string;
  };
  allowances: {
    foodAllowanceInSalary: string;
    fuelAllowanceInSalary: string;
    numberOfMeals: number;
    fuelInLiters: number;
    fuelAmount: string;
    foodProvidedByCompany: boolean;
  };
  insurance: {
    healthInsuranceProvider?: string;
    healthInsurancePolicyNumber?: string;
    healthInsuranceExpiryDate?: string;
    lifeInsuranceProvider?: string;
    lifeInsurancePolicyNumber?: string;
    lifeInsuranceExpiryDate?: string;
  };
  bankDetails: {
    bankName?: string;
    bankBranch?: string;
    accountNumber?: string;
    accountTitle?: string;
    iban?: string;
  };
  accommodation?: {
    accommodationProvidedByEmployer: boolean;
    accommodationType?: string;
    accommodationAddress?: string;
  };
}

export interface EmployeeTrainingData {
  completedTrainings: {
    id: number;
    title: string;
    completionDate: string;
    certificateUrl?: string;
    score?: number;
  }[];
  upcomingTrainings: {
    id: number;
    title: string;
    startDate: string;
    duration: string;
    mandatory: boolean;
  }[];
  trainingRequirements: string[];
}

export interface EmployeeTeamData {
  reportees: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
  manager?: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  };
  colleagues: {
    id: number;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
}

class EmployeePortalService {
  private baseUrl = '/api';

  /**
   * Get comprehensive employee profile by email with all relations
   */
  async getEmployeeProfile(userEmail: string): Promise<EmployeePortalProfile | null> {
    try {
      // Special case for admin user
      if (userEmail === '<EMAIL>') {
        return {
          id: 999999,
          employeeId: 'ADMIN-001',
          firstName: 'Super',
          middleName: '',
          lastName: 'Administrator',
          email: userEmail,
          department: 'IT Department',
          designation: 'System Administrator',
          joinDate: 'Not specified',
          employmentType: 'Full Time',
          reportingManager: 'Not specified',
          location: 'Head Office',
          phone: 'Not specified',
          profileImagePath: undefined,
          gender: 'Not specified',
          dateOfBirth: 'Not specified',
          religion: 'Not specified',
          cnicNumber: 'Not specified',
          cnicExpiryDate: '',
          nationality: 'Not specified',
          maritalStatus: 'Not specified',
          bloodType: 'Not specified',
          fatherName: 'Not specified',
          status: 'Active',
          statusDate: '',
          notes: '',
          specialInstructions: '',
          mobileNumber: 'Not specified',
          officialNumber: '',
          officialEmail: userEmail,
          personalEmail: '',
          permanentAddress: 'Not specified',
          currentAddress: 'Not specified',
          emergencyContactName: 'Not specified',
          emergencyContactPhone: 'Not specified',
          emergencyContactRelationship: 'Not specified',
          linkedinProfile: '',
          otherSocialProfiles: '',
          education: [],
          experience: [],
          family: [],
          skills: [],
          devices: [],
          projects: [],
          vehicles: [],
          healthRecords: []
        };
      }

      // Step 1: Get all employees to find the employee by email
      const employeesResponse = await employeeApi.getAll();
      
      if (!employeesResponse.success) {
        console.error('Failed to fetch employees');
        return null;
      }

      const employees = employeesResponse.data || employeesResponse.employees || [];
      
      // Find employee by email
      const employee = employees.find((emp: any) => {
        const officialEmail = emp.contact?.officialEmail || emp.officialEmail;
        const personalEmail = emp.contact?.personalEmail || emp.personalEmail;
        return officialEmail === userEmail || personalEmail === userEmail || emp.email === userEmail;
      });

      if (!employee) {
        console.error('Employee not found for email:', userEmail);
        return null;
      }

      // Step 2: Fetch detailed employee data with all relations using the comprehensive endpoint
      let detailedEmployee;
      try {
        const detailedResponse = await api.get(`/employees/${employee.id}`);
        detailedEmployee = detailedResponse.data.employee || detailedResponse.data;
        console.log('Fetched detailed employee data:', {
          id: detailedEmployee.id,
          name: `${detailedEmployee.firstName} ${detailedEmployee.lastName}`,
          hasEducation: detailedEmployee.educationEntries?.length > 0,
          hasExperience: detailedEmployee.experienceEntries?.length > 0,
          hasFamily: detailedEmployee.family?.length > 0,
          hasSkills: detailedEmployee.skills?.length > 0,
          hasDevices: detailedEmployee.deviceEntries?.length > 0,
          hasProjects: detailedEmployee.projectEntries?.length > 0,
          hasVehicles: detailedEmployee.vehicles?.length > 0,
          hasHealthRecords: detailedEmployee.healthRecords?.length > 0,
          hasJob: !!detailedEmployee.job,
          hasBenefit: !!detailedEmployee.benefit,
          hasContact: !!detailedEmployee.contact
        });
        
        // Debug: Log the actual structure of related entities
        if (detailedEmployee.job) {
          console.log('Job data structure:', Object.keys(detailedEmployee.job));
        }
        if (detailedEmployee.benefit) {
          console.log('Benefit data structure:', Object.keys(detailedEmployee.benefit));
          console.log('Benefit data sample:', {
            totalSalary: detailedEmployee.benefit.totalSalary,
            salaryTier: detailedEmployee.benefit.salaryTier,
            bankName: detailedEmployee.benefit.bankName,
            paymentMode: detailedEmployee.benefit.paymentMode
          });
        }
        if (detailedEmployee.contact) {
          console.log('Contact data structure:', Object.keys(detailedEmployee.contact));
        }
      } catch (error) {
        console.warn('Could not fetch detailed employee data, using basic data:', error);
        detailedEmployee = employee;
      }

      // Step 3: Build comprehensive profile from the detailed response
      const profile: EmployeePortalProfile = {
        id: detailedEmployee.id,
        employeeId: detailedEmployee.employeeId || `EMP-${detailedEmployee.id}`,
        firstName: detailedEmployee.firstName || 'Not specified',
        middleName: detailedEmployee.middleName || '',
        lastName: detailedEmployee.lastName || 'Not specified',
        email: detailedEmployee.contact?.officialEmail || detailedEmployee.contact?.personalEmail || detailedEmployee.officialEmail || detailedEmployee.personalEmail || detailedEmployee.email || userEmail,
        department: detailedEmployee.job?.department || detailedEmployee.department || 'Not specified',
        designation: detailedEmployee.job?.designation || detailedEmployee.designation || 'Not specified',
        joinDate: detailedEmployee.job?.joinDate || detailedEmployee.joinDate || 'Not specified',
        employmentType: detailedEmployee.job?.employmentType || detailedEmployee.employmentType || 'Full Time',
        reportingManager: detailedEmployee.job?.reportingTo || detailedEmployee.reportingManager || 'Not specified',
        location: detailedEmployee.job?.location || detailedEmployee.location || 'Not specified',
        phone: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        profileImagePath: detailedEmployee.profileImagePath,
        
        // Personal Information
        gender: detailedEmployee.gender || 'Not specified',
        dateOfBirth: detailedEmployee.dateOfBirth || 'Not specified',
        religion: detailedEmployee.religion || 'Not specified',
        cnicNumber: detailedEmployee.cnicNumber || 'Not specified',
        cnicExpiryDate: detailedEmployee.cnicExpiryDate || '',
        nationality: detailedEmployee.nationality || 'Not specified',
        maritalStatus: detailedEmployee.maritalStatus || 'Not specified',
        bloodType: detailedEmployee.bloodType || 'Not specified',
        fatherName: detailedEmployee.fatherName || 'Not specified',
        status: detailedEmployee.status || detailedEmployee.employmentStatus || 'Active',
        statusDate: detailedEmployee.statusDate || '',
        notes: detailedEmployee.notes || '',
        specialInstructions: detailedEmployee.specialInstructions || '',
        
        // Contact Information
        mobileNumber: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        officialNumber: detailedEmployee.contact?.officialNumber || detailedEmployee.officialNumber || '',
        officialEmail: detailedEmployee.contact?.officialEmail || detailedEmployee.officialEmail || '',
        personalEmail: detailedEmployee.contact?.personalEmail || detailedEmployee.personalEmail || '',
        permanentAddress: detailedEmployee.contact?.permanentAddress || detailedEmployee.permanentAddress || 'Not specified',
        currentAddress: detailedEmployee.contact?.currentAddress || detailedEmployee.currentAddress || 'Not specified',
        emergencyContactName: detailedEmployee.contact?.emergencyContactName || detailedEmployee.emergencyContactName || 'Not specified',
        emergencyContactPhone: detailedEmployee.contact?.emergencyContactPhone || detailedEmployee.emergencyContactPhone || 'Not specified',
        emergencyContactRelationship: detailedEmployee.contact?.emergencyContactRelationship || detailedEmployee.emergencyContactRelationship || 'Not specified',
        linkedinProfile: detailedEmployee.contact?.linkedinProfile || detailedEmployee.linkedinProfile || '',
        otherSocialProfiles: detailedEmployee.contact?.otherSocialProfiles || detailedEmployee.otherSocialProfiles || '',
        
        // Education - Use educationEntries from backend response
        education: (detailedEmployee.educationEntries || detailedEmployee.education || []).map((edu: any) => ({
          id: edu.id,
          educationLevel: edu.educationLevel || 'Not specified',
          degree: edu.degree || 'Not specified',
          major: edu.major || '',
          institution: edu.institution || 'Not specified',
          graduationYear: edu.graduationYear || 'Not specified',
          grade: edu.grade || ''
        })),
        
        // Experience - Use experienceEntries from backend response
        experience: (detailedEmployee.experienceEntries || detailedEmployee.experience || []).map((exp: any) => ({
          id: exp.id,
          companyName: exp.companyName || 'Not specified',
          position: exp.position || 'Not specified',
          startDate: exp.startDate || 'Not specified',
          endDate: exp.endDate || '',
          description: exp.description || '',
          salary: exp.salary || '',
          reasonForLeaving: exp.reasonForLeaving || ''
        })),
        
        // Family - Backend splits family into children and dependents, but we need the original family array
        family: [
          ...(detailedEmployee.children || []),
          ...(detailedEmployee.dependents || []),
          // Add spouse if exists
          ...(detailedEmployee.spouseName ? [{
            id: Date.now(),
            name: detailedEmployee.spouseName,
            dateOfBirth: detailedEmployee.spouseDateOfBirth || '',
            relationship: 'spouse',
            gender: '',
            cnic: detailedEmployee.spouseCNIC || '',
            occupation: detailedEmployee.spouseOccupation || '',
            employer: detailedEmployee.spouseEmployer || '',
            contactNumber: detailedEmployee.spouseContactNumber || '',
            type: 'spouse'
          }] : [])
        ].map((fam: any) => ({
          id: fam.id,
          name: fam.name || 'Not specified',
          dateOfBirth: fam.dateOfBirth || '',
          relationship: fam.relationship || 'Not specified',
          gender: fam.gender || '',
          cnic: fam.cnic || '',
          occupation: fam.occupation || '',
          employer: fam.employer || '',
          contactNumber: fam.contactNumber || '',
          type: fam.type || 'dependent'
        })),
        
        // Skills - Backend flattens skills to top level fields
        skills: [{
          id: 1,
          professionalSkills: detailedEmployee.professionalSkills || '',
          technicalSkills: detailedEmployee.technicalSkills || '',
          certifications: detailedEmployee.certifications || '',
          languages: detailedEmployee.languages || ''
        }],
        
        // Devices - Use deviceEntries from backend response
        devices: (detailedEmployee.deviceEntries || detailedEmployee.devices || []).map((device: any) => ({
          id: device.id,
          deviceType: device.deviceType || 'Not specified',
          deviceBrand: device.deviceBrand || '',
          deviceModel: device.deviceModel || '',
          serialNumber: device.serialNumber || '',
          assignedDate: device.assignedDate || 'Not specified',
          returnDate: device.returnDate || '',
          condition: device.condition || '',
          notes: device.notes || ''
        })),
        
        // Projects - Use projectEntries from backend response
        projects: (detailedEmployee.projectEntries || detailedEmployee.projects || []).map((project: any) => ({
          id: project.id,
          projectName: project.projectName || 'Not specified',
          role: project.role || 'Not specified',
          startDate: project.startDate || 'Not specified',
          endDate: project.endDate || '',
          description: project.description || '',
          status: project.status || 'Unknown'
        })),
        
        // Vehicles - Backend flattens vehicles to top level fields
        vehicles: detailedEmployee.vehicleType ? [{
          id: 1,
          vehicleType: detailedEmployee.vehicleType || 'Not specified',
          make: '',
          model: detailedEmployee.vehicleMakeModel || '',
          year: '',
          licensePlate: detailedEmployee.registrationNumber || '',
          assignedDate: detailedEmployee.handingOverDate || 'Not specified',
          returnDate: detailedEmployee.returnDate || ''
        }] : [],
        
        // Health Records - Backend flattens health records to top level fields
        healthRecords: detailedEmployee.medicalHistory ? [{
          id: 1,
          recordType: 'Medical History',
          recordDate: new Date().toISOString().split('T')[0],
          description: detailedEmployee.medicalHistory || '',
          doctorName: '',
          hospitalName: '',
          medication: detailedEmployee.regularMedications || '',
          notes: `Blood Group: ${detailedEmployee.bloodGroup || 'N/A'}, Allergies: ${detailedEmployee.allergies || 'N/A'}, Chronic Conditions: ${detailedEmployee.chronicConditions || 'N/A'}`
        }] : [],

        // Employment Details - Additional fields from job relation
        employmentStatus: detailedEmployee.job?.employmentStatus || detailedEmployee.employmentStatus,
        employeeLevel: detailedEmployee.job?.employeeLevel || detailedEmployee.employeeLevel,
        probationEndDate: detailedEmployee.job?.probationEndDate || detailedEmployee.probationEndDate,
        noticePeriod: detailedEmployee.job?.noticePeriod || detailedEmployee.noticePeriod,
        reportingTo: detailedEmployee.job?.reportingTo || detailedEmployee.reportingTo,
        remoteWorkEligible: detailedEmployee.job?.remoteWorkEligible || detailedEmployee.remoteWorkEligible,
        nextReviewDate: detailedEmployee.job?.nextReviewDate || detailedEmployee.nextReviewDate,
        trainingRequirements: detailedEmployee.job?.trainingRequirements || detailedEmployee.trainingRequirements,
        workSchedule: detailedEmployee.job?.workSchedule || detailedEmployee.workSchedule,
        shiftType: detailedEmployee.job?.shiftType || detailedEmployee.shiftType,
        project: detailedEmployee.job?.project || detailedEmployee.project,

        // Compensation & Benefits - From benefit relation
        totalSalary: detailedEmployee.benefit?.totalSalary || detailedEmployee.totalSalary,
        salaryTier: detailedEmployee.benefit?.salaryTier || detailedEmployee.salaryTier,
        salaryTrench: detailedEmployee.benefit?.salaryTrench || detailedEmployee.salaryTrench,
        cashAmount: detailedEmployee.benefit?.cashAmount || detailedEmployee.cashAmount,
        bankAmount: detailedEmployee.benefit?.bankAmount || detailedEmployee.bankAmount,
        paymentMode: detailedEmployee.benefit?.paymentMode || detailedEmployee.paymentMode,

        // Allowances - From benefit relation
        foodAllowanceInSalary: detailedEmployee.benefit?.foodAllowanceInSalary || detailedEmployee.foodAllowanceInSalary,
        fuelAllowanceInSalary: detailedEmployee.benefit?.fuelAllowanceInSalary || detailedEmployee.fuelAllowanceInSalary,
        numberOfMeals: detailedEmployee.benefit?.numberOfMeals || detailedEmployee.numberOfMeals,
        fuelInLiters: detailedEmployee.benefit?.fuelInLiters || detailedEmployee.fuelInLiters,
        fuelAmount: detailedEmployee.benefit?.fuelAmount || detailedEmployee.fuelAmount,
        foodProvidedByCompany: detailedEmployee.benefit?.foodProvidedByCompany || detailedEmployee.foodProvidedByCompany,

        // Bank Details - From benefit relation
        bankName: detailedEmployee.benefit?.bankName || detailedEmployee.bankName,
        bankBranch: detailedEmployee.benefit?.bankBranch || detailedEmployee.bankBranch,
        accountNumber: detailedEmployee.benefit?.accountNumber || detailedEmployee.accountNumber,
        accountTitle: detailedEmployee.benefit?.accountTitle || detailedEmployee.accountTitle,
        iban: detailedEmployee.benefit?.iban || detailedEmployee.iban,

        // Insurance - From benefit relation
        healthInsuranceProvider: detailedEmployee.benefit?.healthInsuranceProvider || detailedEmployee.healthInsuranceProvider,
        healthInsurancePolicyNumber: detailedEmployee.benefit?.healthInsurancePolicyNumber || detailedEmployee.healthInsurancePolicyNumber,
        healthInsuranceExpiryDate: detailedEmployee.benefit?.healthInsuranceExpiryDate || detailedEmployee.healthInsuranceExpiryDate,
        lifeInsuranceProvider: detailedEmployee.benefit?.lifeInsuranceProvider || detailedEmployee.lifeInsuranceProvider,
        lifeInsurancePolicyNumber: detailedEmployee.benefit?.lifeInsurancePolicyNumber || detailedEmployee.lifeInsurancePolicyNumber,
        lifeInsuranceExpiryDate: detailedEmployee.benefit?.lifeInsuranceExpiryDate || detailedEmployee.lifeInsuranceExpiryDate,

        // Accommodation - From benefit relation
        accommodationProvidedByEmployer: detailedEmployee.benefit?.accommodationProvidedByEmployer || detailedEmployee.accommodationProvidedByEmployer,
        accommodationType: detailedEmployee.benefit?.accommodationType || detailedEmployee.accommodationType,
        accommodationAddress: detailedEmployee.benefit?.accommodationAddress || detailedEmployee.accommodationAddress
      };

      console.log('Built comprehensive profile with:', {
        educationCount: profile.education.length,
        experienceCount: profile.experience.length,
        familyCount: profile.family.length,
        skillsCount: profile.skills.length,
        devicesCount: profile.devices.length,
        projectsCount: profile.projects.length,
        vehiclesCount: profile.vehicles.length,
        healthRecordsCount: profile.healthRecords.length,
        compensation: {
          totalSalary: profile.totalSalary,
          salaryTier: profile.salaryTier,
          bankName: profile.bankName,
          paymentMode: profile.paymentMode,
          foodAllowanceInSalary: profile.foodAllowanceInSalary,
          fuelAllowanceInSalary: profile.fuelAllowanceInSalary
        }
      });

      return profile;
      
    } catch (error) {
      console.error('Error fetching comprehensive employee profile:', error);
      return null;
    }
  }

  /**
   * Get employee leave data including balances and recent requests
   */
  async getEmployeeLeaveData(employeeId: number): Promise<EmployeeLeaveData> {
    try {
      console.log('Fetching leave data for employee ID:', employeeId);
      
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for leave data:', employeeId);
        return this.getEmptyLeaveData();
      }
      
      // Use the same calculation service that Leave Management uses
      const currentYear = new Date().getFullYear();
      
      console.log(`🔍 Fetching calculated leave balances for employee ${employeeId} using calculation service...`);
      
      // Use the calculation service endpoint directly
      const calculationResponse = await fetch(`/api/leave-balances/all-active-employees?year=${currentYear}&limit=1000`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      
      if (!calculationResponse.ok) {
        console.error(`Failed to fetch calculated balances: ${calculationResponse.status}`);
        return this.getEmptyLeaveData();
      }
      
      const calculationData = await calculationResponse.json();
      
      if (!calculationData.success || !calculationData.data) {
        console.warn('Calculation service returned no data');
        return this.getEmptyLeaveData();
      }
      
      // Find the specific employee in the calculated data
      const employee = calculationData.data.find((emp: any) => emp.employeeId === employeeId);
      
      if (!employee || !employee.balances || employee.balances.length === 0) {
        console.warn(`Employee ${employeeId} not found in calculated balances or has no balances`);
        return this.getEmptyLeaveData();
      }
      
      const balances = employee.balances;
      
      // Fetch recent leave requests - for now using getAll and filtering client-side
      const requestsResponse = await leaveRequestsApi.getAll();
      const allRequests = requestsResponse.data || [];
      const requests = allRequests.filter((req: any) => req.employeeId === employeeId);

      // Calculate totals
      const totalDaysUsed = balances.reduce((sum: number, balance: any) => sum + (balance.used || 0), 0);
      const totalDaysRemaining = balances.reduce((sum: number, balance: any) => sum + (balance.remaining || 0), 0);
      const pendingRequestsCount = requests.filter((req: any) => req.status === 'pending').length;

      // Get recent requests (last 5)
      const recentRequests = requests
        .sort((a: any, b: any) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
        .slice(0, 5)
        .map((req: any) => ({
          id: req.id,
          type: req.leaveType,
          from: req.startDate,
          to: req.endDate,
          status: req.status,
          days: req.totalDays || 1,
          reason: req.reason
        }));

              console.log(`✅ Returning leave data for employee ${employeeId}:`, {
          balancesCount: balances.length,
          recentRequestsCount: recentRequests.length,
          totalDaysUsed,
          totalDaysRemaining,
          actualBalances: balances.map((b: any) => ({
            leaveType: b.leaveType,
            total: b.total || b.totalAllocated || 0,
            used: b.used || 0,
            remaining: b.remaining || 0
          }))
        });

      return {
        balances: balances.map((balance: any) => ({
          leaveType: balance.leaveType,
          total: balance.total || balance.totalAllocated || 0,
          used: balance.used || 0,
          pending: balance.pending || 0,
          remaining: balance.remaining || 0,
          carryForward: balance.carryForward || balance.carriedForward || 0,
          expiryDate: balance.expiryDate
        })),
        recentRequests,
        pendingRequestsCount,
        totalDaysUsed,
        totalDaysRemaining
      };
    } catch (error) {
      console.error('Error fetching employee leave data:', error);
      console.log('Returning empty leave data due to API failure');
      return this.getEmptyLeaveData();
    }
  }

  private getEmptyLeaveData(): EmployeeLeaveData {
    return {
      balances: [],
      recentRequests: [],
      pendingRequestsCount: 0,
      totalDaysUsed: 0,
      totalDaysRemaining: 0
    };
  }

  /**
   * Get employee payroll data
   */
  async getEmployeePayrollData(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for payroll:', employeeId);
        return this.getMockPayrollData();
      }
      
      // Try to fetch from payroll API
      const response = await api.get(`/employees/${employeeId}/payroll`);
      
      if (response.data) {
        return response.data;
      }
      
      // Fallback to mock data
      return this.getMockPayrollData();
    } catch (error) {
      console.error('Error fetching employee payroll data:', error);
      console.log('Returning mock payroll data due to API failure');
      return this.getMockPayrollData();
    }
  }

  private getMockPayrollData(): EmployeePayrollData {
    return {
      lastPayment: {
        date: '2024-01-31',
        amount: '75,000',
        currency: 'PKR'
      },
      ytdEarnings: '825,000 PKR',
      payslips: [
        {
          id: '1',
          period: 'January 2024',
          date: '2024-01-31',
          amount: '75,000',
          status: 'paid'
        },
        {
          id: '2',
          period: 'December 2023',
          date: '2023-12-31',
          amount: '75,000',
          status: 'paid'
        },
        {
          id: '3',
          period: 'November 2023',
          date: '2023-11-30',
          amount: '75,000',
          status: 'paid'
        }
      ]
    };
  }

  /**
   * Get employee performance data
   */
  async getEmployeePerformanceData(employeeId: number): Promise<EmployeePerformanceData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for performance:', employeeId);
        return this.getMockPerformanceData();
      }
      
      // Try to fetch from performance API
      const response = await api.get(`/employees/${employeeId}/performance`);
      
      if (response.data) {
        return response.data;
      }
      
      // Return mock data if no response
      return this.getMockPerformanceData();
    } catch (error) {
      console.error('Error fetching employee performance data:', error);
      console.log('Returning mock performance data due to API failure');
      return this.getMockPerformanceData();
    }
  }

  private getMockPerformanceData(): EmployeePerformanceData {
    return {
      currentRating: 4.2,
      lastReviewDate: '2023-12-15',
      nextReviewDate: '2024-06-15',
      goals: [
        {
          id: 1,
          title: 'Complete Project Alpha',
          status: 'in_progress',
          progress: 75,
          deadline: '2024-03-31'
        },
        {
          id: 2,
          title: 'Improve Team Collaboration',
          status: 'completed',
          progress: 100,
          deadline: '2024-01-31'
        },
        {
          id: 3,
          title: 'Learn New Technology Stack',
          status: 'pending',
          progress: 25,
          deadline: '2024-06-30'
        }
      ],
      achievements: [
        {
          id: 1,
          title: 'Employee of the Month',
          description: 'Outstanding performance in Q4 2023',
          date: '2023-12-01',
          type: 'award'
        },
        {
          id: 2,
          title: 'Project Delivery Excellence',
          description: 'Delivered Project Beta ahead of schedule',
          date: '2023-11-15',
          type: 'recognition'
        }
      ]
    };
  }

  /**
   * Get employee documents
   */
  async getEmployeeDocuments(employeeId: number): Promise<EmployeeDocumentData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for documents:', employeeId);
        return this.getMockDocumentsData();
      }
      
      const response = await api.get(`/employees/${employeeId}/documents`);
      
      if (response.data && response.data.documents) {
        return {
          documents: response.data.documents.map((doc: any) => ({
            id: doc.id,
            name: doc.fileName || doc.name,
            type: doc.documentType || 'Document',
            uploadDate: doc.uploadDate || doc.createdAt,
            size: doc.fileSize || 'N/A',
            downloadUrl: doc.filePath || '#'
          })),
          totalCount: response.data.documents.length
        };
      }
      
      return this.getMockDocumentsData();
    } catch (error) {
      console.error('Error fetching employee documents:', error);
      console.log('Returning mock documents data due to API failure');
      return this.getMockDocumentsData();
    }
  }

  private getMockDocumentsData(): EmployeeDocumentData {
    return {
      documents: [
        {
          id: 1,
          name: 'Employment Contract.pdf',
          type: 'Contract',
          uploadDate: '2023-06-15',
          size: '2.5 MB',
          downloadUrl: '#'
        },
        {
          id: 2,
          name: 'Employee Handbook.pdf',
          type: 'Policy',
          uploadDate: '2023-06-16',
          size: '1.8 MB',
          downloadUrl: '#'
        },
        {
          id: 3,
          name: 'CNIC Copy.jpg',
          type: 'ID Document',
          uploadDate: '2023-06-15',
          size: '800 KB',
          downloadUrl: '#'
        },
        {
          id: 4,
          name: 'Educational Certificates.pdf',
          type: 'Education',
          uploadDate: '2023-06-15',
          size: '3.2 MB',
          downloadUrl: '#'
        }
      ],
      totalCount: 4
    };
  }

  /**
   * Update employee profile information
   */
  async updateEmployeeProfile(employeeId: number, profileData: Partial<EmployeePortalProfile>): Promise<boolean> {
    try {
      const response = await api.put(`/employees/${employeeId}`, profileData);
      return response.data?.success || false;
    } catch (error) {
      console.error('Error updating employee profile:', error);
      return false;
    }
  }

  /**
   * Submit leave request
   */
  async submitLeaveRequest(employeeId: number, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    totalDays: number;
  }): Promise<boolean> {
    try {
      // Direct API call since create method doesn't exist in leaveRequestsApi
      const token = localStorage.getItem('authToken');
      const response = await api.post('/api/leave-requests', {
        employeeId,
        ...leaveData
      }, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      return response.data?.success || false;
    } catch (error) {
      console.error('Error submitting leave request:', error);
      return false;
    }
  }

  /**
   * Get comprehensive employee attendance data
   */
  async getEmployeeAttendanceData(employeeId: number): Promise<EmployeeAttendanceData> {
    try {
      console.log('Fetching attendance data for employee ID:', employeeId);
      
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided:', employeeId);
        return this.getMockAttendanceData();
      }
      
      // Try to fetch from attendance API
      const attendanceResponse = await api.get(`/api/attendance/employee/${employeeId}`);
      const attendanceRecords = attendanceResponse.data?.data || attendanceResponse.data || [];
      
      console.log('Attendance records fetched:', attendanceRecords.length);
      
      // Return the full attendance records for the Employee Portal
      return {
        summary: {
          currentMonth: {
            totalDays: attendanceRecords.length,
            presentDays: attendanceRecords.filter((a: any) => a.status === 'present' || a.status === 'work_from_home').length,
            absentDays: attendanceRecords.filter((a: any) => a.status === 'absent').length,
            leaveDays: attendanceRecords.filter((a: any) => a.status === 'leave').length,
            holidayDays: 0,
            workingDays: attendanceRecords.length,
            punctualityScore: 85
          },
          recentAttendance: attendanceRecords.slice(0, 10).map((record: any) => ({
            date: record.date,
            checkIn: record.checkInTime || 'N/A',
            checkOut: record.checkOutTime || 'N/A',
            status: record.status || 'present',
            totalHours: record.workHours || 0
          }))
        },
        allAttendanceRecords: attendanceRecords
      };
    } catch (error) {
      console.error('Error fetching employee attendance data:', error);
      console.log('Returning mock attendance data due to API failure');
      return this.getMockAttendanceData();
    }
  }

  private getMockAttendanceData(): EmployeeAttendanceData {
    // Generate mock attendance data for demonstration
    const mockAttendanceRecords = [];
    const today = new Date();
    
    for (let i = 30; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      const status = Math.random() > 0.9 ? 'absent' : Math.random() > 0.8 ? 'late' : 'present';
      const checkInTime = status === 'absent' ? '-' : `0${8 + Math.floor(Math.random() * 2)}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
      const checkOutTime = status === 'absent' ? '-' : `1${7 + Math.floor(Math.random() * 2)}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
      const workHours = status === 'absent' ? 0 : 7.5 + Math.random() * 2;
      
      mockAttendanceRecords.push({
        id: i + 1,
        employeeId: 1,
        employeeName: 'Mock Employee',
        date: date.toISOString().split('T')[0],
        checkInTime,
        checkOutTime,
        status,
        workHours,
        overtime: workHours > 8 ? workHours - 8 : 0,
        location: 'Office',
        shift: 1,
        shiftName: 'Morning Shift',
        department: 'IT',
        isRemote: false
      });
    }
    
    return {
      summary: {
        currentMonth: {
          totalDays: mockAttendanceRecords.length,
          presentDays: mockAttendanceRecords.filter(a => a.status === 'present').length,
          absentDays: mockAttendanceRecords.filter(a => a.status === 'absent').length,
          leaveDays: 0,
          holidayDays: 0,
          workingDays: mockAttendanceRecords.length,
          punctualityScore: 85
        },
        recentAttendance: mockAttendanceRecords.slice(0, 10).map(record => ({
          date: record.date,
          checkIn: record.checkInTime,
          checkOut: record.checkOutTime,
          status: record.status as any,
          totalHours: record.workHours
        }))
      },
      allAttendanceRecords: mockAttendanceRecords
    };
  }

  /**
   * Get employee benefits and salary data
   */
  async getEmployeeBenefitsData(employeeId: number): Promise<EmployeeBenefitsData> {
    try {
      console.log('Fetching benefits data for employee ID:', employeeId);
      
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for benefits:', employeeId);
        return this.getMockBenefitsData();
      }
      
      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const benefit = employee.benefit || {};
      
      return {
        salary: {
          totalSalary: benefit.totalSalary || '0',
          salaryTier: benefit.salaryTier || 'N/A',
          salaryTrench: benefit.salaryTrench || 'N/A',
          cashAmount: benefit.cashAmount || '0',
          bankAmount: benefit.bankAmount || '0',
          paymentMode: benefit.paymentMode || 'N/A'
        },
        allowances: {
          foodAllowanceInSalary: benefit.foodAllowanceInSalary || '0',
          fuelAllowanceInSalary: benefit.fuelAllowanceInSalary || '0',
          numberOfMeals: benefit.numberOfMeals || 0,
          fuelInLiters: benefit.fuelInLiters || 0,
          fuelAmount: benefit.fuelAmount || '0',
          foodProvidedByCompany: benefit.foodProvidedByCompany || false
        },
        insurance: {
          healthInsuranceProvider: benefit.healthInsuranceProvider,
          healthInsurancePolicyNumber: benefit.healthInsurancePolicyNumber,
          healthInsuranceExpiryDate: benefit.healthInsuranceExpiryDate,
          lifeInsuranceProvider: benefit.lifeInsuranceProvider,
          lifeInsurancePolicyNumber: benefit.lifeInsurancePolicyNumber,
          lifeInsuranceExpiryDate: benefit.lifeInsuranceExpiryDate
        },
        bankDetails: {
          bankName: benefit.bankName,
          bankBranch: benefit.bankBranch,
          accountNumber: benefit.accountNumber,
          accountTitle: benefit.accountTitle,
          iban: benefit.iban
        },
        accommodation: {
          accommodationProvidedByEmployer: benefit.accommodationProvidedByEmployer || false,
          accommodationType: benefit.accommodationType,
          accommodationAddress: benefit.accommodationAddress
        }
      };
    } catch (error) {
      console.error('Error fetching employee benefits data:', error);
      console.log('Returning mock benefits data due to API failure');
      return this.getMockBenefitsData();
    }
  }

  private getMockBenefitsData(): EmployeeBenefitsData {
    return {
      salary: {
        totalSalary: '75,000',
        salaryTier: 'Mid-Level',
        salaryTrench: 'T2',
        cashAmount: '25,000',
        bankAmount: '50,000',
        paymentMode: 'Bank Transfer'
      },
      allowances: {
        foodAllowanceInSalary: '5,000',
        fuelAllowanceInSalary: '8,000',
        numberOfMeals: 2,
        fuelInLiters: 30,
        fuelAmount: '8,000',
        foodProvidedByCompany: true
      },
      insurance: {
        healthInsuranceProvider: 'EFU Life Insurance',
        healthInsurancePolicyNumber: 'HI-2024-001',
        healthInsuranceExpiryDate: '2024-12-31',
        lifeInsuranceProvider: 'State Life Insurance',
        lifeInsurancePolicyNumber: 'LI-2024-001',
        lifeInsuranceExpiryDate: '2025-12-31'
      },
      bankDetails: {
        bankName: 'Habib Bank Limited',
        bankBranch: 'Main Branch',
        accountNumber: '**********',
        accountTitle: 'John Doe',
        iban: '************************'
      },
      accommodation: {
        accommodationProvidedByEmployer: false,
        accommodationType: '',
        accommodationAddress: ''
      }
    };
  }

  /**
   * Get employee training data
   */
  async getEmployeeTrainingData(employeeId: number): Promise<EmployeeTrainingData> {
    try {
      console.log('Fetching training data for employee ID:', employeeId);
      
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for training:', employeeId);
        return this.getMockTrainingData();
      }
      
      // Get employee details with training requirements
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const trainingRequirements = (employee as any).job?.trainingRequirements 
        ? (employee as any).job.trainingRequirements.split(',').map((req: string) => req.trim()).filter(Boolean)
        : [];
      
      // For now, return structured data with training requirements from job
      // TODO: Integrate with actual training management system when available
      return {
        completedTrainings: [],
        upcomingTrainings: [],
        trainingRequirements
      };
    } catch (error) {
      console.error('Error fetching employee training data:', error);
      console.log('Returning mock training data due to API failure');
      return this.getMockTrainingData();
    }
  }

  private getMockTrainingData(): EmployeeTrainingData {
    return {
      completedTrainings: [
        {
          id: 1,
          title: 'IT Security Awareness',
          completionDate: '2023-11-15',
          certificateUrl: '#',
          score: 92
        },
        {
          id: 2,
          title: 'Microsoft Office Suite',
          completionDate: '2023-10-20',
          certificateUrl: '#',
          score: 87
        }
      ],
      upcomingTrainings: [
        {
          id: 3,
          title: 'Project Management Fundamentals',
          startDate: '2024-02-15',
          duration: '3 days',
          mandatory: true
        },
        {
          id: 4,
          title: 'Communication Skills Workshop',
          startDate: '2024-03-01',
          duration: '2 days',
          mandatory: false
        }
      ],
      trainingRequirements: [
        'IT Security Training',
        'Customer Service Excellence',
        'Time Management'
      ]
    };
  }

  /**
   * Get employee team data (manager, reportees, colleagues)
   */
  async getEmployeeTeamData(employeeId: number): Promise<EmployeeTeamData> {
    try {
      console.log('Fetching team data for employee ID:', employeeId);
      
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for team data:', employeeId);
        return this.getMockTeamData();
      }
      
      // Get all employees to find team relationships
      const response = await employeeApi.getAll();
      
      if (!response.success) {
        throw new Error('Failed to fetch employees');
      }
      
      const employees = response.data || response.employees || [];
      const currentEmployee = employees.find((emp: any) => emp.id === employeeId);
      
      if (!currentEmployee) {
        throw new Error('Current employee not found');
      }
      
      const currentDepartment = (currentEmployee as any).job?.department || (currentEmployee as any).department;
      const reportingTo = (currentEmployee as any).job?.reportingTo;
      
      // Find manager by name (if reportingTo is specified)
      const manager = reportingTo 
        ? employees.find((emp: any) => 
            `${emp.firstName} ${emp.lastName}`.toLowerCase().includes(reportingTo.toLowerCase())
          )
        : undefined;
      
      // Find reportees (employees who report to this employee)
      const reportees = employees.filter((emp: any) => {
        const empReportingTo = emp.job?.reportingTo;
        const currentEmployeeName = `${currentEmployee.firstName} ${currentEmployee.lastName}`;
        return empReportingTo && empReportingTo.toLowerCase().includes(currentEmployeeName.toLowerCase());
      });
      
      // Find colleagues (same department, excluding self and reportees)
      const colleagues = employees.filter((emp: any) => {
        const empDepartment = emp.job?.department || emp.department;
        return emp.id !== employeeId && 
               empDepartment === currentDepartment &&
               !reportees.some((reportee: any) => reportee.id === emp.id);
      }).slice(0, 10); // Limit to 10 colleagues
      
      const formatEmployee = (emp: any) => ({
        id: emp.id,
        name: `${emp.firstName} ${emp.lastName}`,
        designation: emp.job?.designation || emp.designation || 'N/A',
        department: emp.job?.department || emp.department || 'N/A',
        email: emp.contact?.officialEmail || emp.officialEmail || 'N/A'
      });
      
      return {
        reportees: reportees.map(formatEmployee),
        manager: manager ? formatEmployee(manager) : undefined,
        colleagues: colleagues.map(formatEmployee)
      };
    } catch (error) {
      console.error('Error fetching employee team data:', error);
      console.log('Returning mock team data due to API failure');
      return this.getMockTeamData();
    }
  }

  private getMockTeamData(): EmployeeTeamData {
    return {
      reportees: [
        {
          id: 101,
          name: 'Sarah Johnson',
          designation: 'Junior Developer',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 102,
          name: 'Ahmed Khan',
          designation: 'QA Analyst',
          department: 'IT',
          email: '<EMAIL>'
        }
      ],
      manager: {
        id: 50,
        name: 'Michael Smith',
        designation: 'IT Manager',
        department: 'IT',
        email: '<EMAIL>'
      },
      colleagues: [
        {
          id: 103,
          name: 'Lisa Brown',
          designation: 'Senior Developer',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 104,
          name: 'David Wilson',
          designation: 'System Administrator',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 105,
          name: 'Emily Davis',
          designation: 'Business Analyst',
          department: 'IT',
          email: '<EMAIL>'
        }
      ]
    };
  }

  /**
   * Enhanced payroll data fetching with real employee benefit data
   */
  async getEmployeePayrollDataEnhanced(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);
      
      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }
      
      const employee = response.data as any;
      const benefit = employee.benefit || {};
      
      return {
        lastPayment: {
          date: new Date().toISOString().split('T')[0],
          amount: benefit.totalSalary || '0',
          currency: 'PKR'
        },
        ytdEarnings: (parseFloat(benefit.totalSalary || '0') * 12).toString(),
        payslips: [], // TODO: Integrate with actual payroll system
        bankDetails: {
          bankName: benefit.bankName || 'N/A',
          accountNumber: benefit.accountNumber || 'N/A'
        }
      };
    } catch (error) {
      console.error('Error fetching enhanced payroll data:', error);
      return {
        lastPayment: {
          date: 'N/A',
          amount: 'N/A',
          currency: 'PKR'
        },
        ytdEarnings: 'N/A',
        payslips: []
      };
    }
  }
}

export const employeePortalService = new EmployeePortalService();
export default employeePortalService; 