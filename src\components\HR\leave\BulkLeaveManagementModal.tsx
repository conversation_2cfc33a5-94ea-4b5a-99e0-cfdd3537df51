import React, { useState, useEffect } from 'react';
import { 
  X, 
  Users, 
  Calendar, 
  Clock, 
  Building2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Al<PERSON>Triangle, 
  CheckCircle, 
  Download,
  Upload,
  Filter,
  Search,
  Plus,
  Trash2,
  Eye,
  FileText,
  Settings
} from 'lucide-react';
import { employeeApi, Employee } from '../../../services/employeeApi';
import { leaveTypesApi, CustomLeaveType } from '../../../services/leaveApi';
import { bulkLeaveApi } from '../../../services/bulkLeaveApi';
import { LeaveStatus } from '../../../types/attendance';

interface BulkLeaveManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface SelectedEmployee {
  id: number;
  employeeId: string;
  name: string;
  department: string;
  designation: string;
  email?: string;
}

interface BulkLeaveRequest {
  leaveType: string;
  startDate: string;
  endDate: string;
  reason: string;
  isHalfDay: boolean;
  applyToAll: boolean;
  selectedEmployees: number[];
  departmentFilter: string[];
  designationFilter: string[];
  autoApprove: boolean;
  notifyEmployees: boolean;
  attachments: File[];
}

const BulkLeaveManagementModal: React.FC<BulkLeaveManagementModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError
}) => {
  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<CustomLeaveType[]>([]);
  const [departments, setDepartments] = useState<string[]>([]);
  const [designations, setDesignations] = useState<string[]>([]);
  
  // Form data
  const [bulkLeaveData, setBulkLeaveData] = useState<BulkLeaveRequest>({
    leaveType: '',
    startDate: '',
    endDate: '',
    reason: '',
    isHalfDay: false,
    applyToAll: false,
    selectedEmployees: [],
    departmentFilter: [],
    designationFilter: [],
    autoApprove: false,
    notifyEmployees: true,
    attachments: []
  });

  // Filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [designationFilter, setDesignationFilter] = useState('all');
  const [showPreview, setShowPreview] = useState(false);

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      loadInitialData();
    }
  }, [isOpen]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // Load employees
      const employeesResponse = await employeeApi.getActiveEmployees();
      if (employeesResponse.success && employeesResponse.data) {
        setEmployees(employeesResponse.data);
        
        // Extract unique departments and designations
        const uniqueDepartments = [...new Set(employeesResponse.data.map(emp => emp.department))].filter(Boolean);
        const uniqueDesignations = [...new Set(employeesResponse.data.map(emp => emp.designation))].filter(Boolean);
        
        setDepartments(uniqueDepartments);
        setDesignations(uniqueDesignations);
      }

      // Load leave types
      const leaveTypesResponse = await leaveTypesApi.getAll();
      if (leaveTypesResponse.success && leaveTypesResponse.data) {
        setLeaveTypes(leaveTypesResponse.data.filter(lt => lt.isActive));
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      onError('Failed to load required data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter employees based on current filters
  const filteredEmployees = employees.filter(emp => {
    const matchesSearch = searchTerm === '' || 
      emp.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = departmentFilter === 'all' || emp.department === departmentFilter;
    const matchesDesignation = designationFilter === 'all' || emp.designation === designationFilter;
    
    return matchesSearch && matchesDepartment && matchesDesignation;
  });

  // Calculate total days
  const calculateDays = (startDate: string, endDate: string, isHalfDay: boolean): number => {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return isHalfDay ? 0.5 : days;
  };

  // Handle employee selection
  const handleEmployeeSelection = (employeeId: number, selected: boolean) => {
    setBulkLeaveData(prev => ({
      ...prev,
      selectedEmployees: selected 
        ? [...prev.selectedEmployees, employeeId]
        : prev.selectedEmployees.filter(id => id !== employeeId)
    }));
  };

  // Handle select all employees
  const handleSelectAll = (selectAll: boolean) => {
    setBulkLeaveData(prev => ({
      ...prev,
      selectedEmployees: selectAll ? filteredEmployees.map(emp => emp.id) : []
    }));
  };

  // Validate form data
  const validateForm = (): string[] => {
    const errors: string[] = [];
    
    if (!bulkLeaveData.leaveType) errors.push('Please select a leave type');
    if (!bulkLeaveData.startDate) errors.push('Please select start date');
    if (!bulkLeaveData.endDate) errors.push('Please select end date');
    if (!bulkLeaveData.reason.trim()) errors.push('Please provide a reason');
    if (bulkLeaveData.selectedEmployees.length === 0) errors.push('Please select at least one employee');
    
    if (bulkLeaveData.startDate && bulkLeaveData.endDate) {
      const startDate = new Date(bulkLeaveData.startDate);
      const endDate = new Date(bulkLeaveData.endDate);
      if (startDate > endDate) {
        errors.push('End date must be after start date');
      }
    }
    
    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      onError(errors.join(', '));
      return;
    }

    setIsLoading(true);
    try {
      // Create leave requests for selected employees
      const selectedEmployeeData = employees.filter(emp =>
        bulkLeaveData.selectedEmployees.includes(emp.id)
      );

      const leaveRequests = selectedEmployeeData.map(employee => ({
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        leaveType: bulkLeaveData.leaveType,
        startDate: bulkLeaveData.startDate,
        endDate: bulkLeaveData.endDate,
        reason: bulkLeaveData.reason,
        isHalfDay: bulkLeaveData.isHalfDay,
        status: bulkLeaveData.autoApprove ? LeaveStatus.APPROVED : LeaveStatus.PENDING,
        daysRequested: calculateDays(bulkLeaveData.startDate, bulkLeaveData.endDate, bulkLeaveData.isHalfDay),
        autoApprove: bulkLeaveData.autoApprove,
        notifyEmployee: bulkLeaveData.notifyEmployees
      }));

      // Create bulk leave requests using the API
      const response = await bulkLeaveApi.createBulkLeave({
        requests: leaveRequests,
        options: {
          autoApprove: bulkLeaveData.autoApprove,
          notifyEmployees: bulkLeaveData.notifyEmployees,
          skipValidation: false,
          createdBy: 'HR Admin', // TODO: Get from auth context
          reason: bulkLeaveData.reason
        }
      });

      if (response.success) {
        const { successful, failed, summary } = response.data;

        if (summary.failed > 0) {
          onError(`Created ${summary.successful} requests successfully, but ${summary.failed} failed. Check the details for more information.`);
        } else {
          onSuccess(`Successfully created ${summary.successful} leave requests${summary.autoApproved > 0 ? ` (${summary.autoApproved} auto-approved)` : ''}`);
        }

        handleClose();
      } else {
        onError(response.message || 'Failed to create leave requests');
      }
    } catch (error) {
      console.error('Error creating bulk leave requests:', error);
      onError('Failed to create leave requests. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setCurrentStep(1);
    setBulkLeaveData({
      leaveType: '',
      startDate: '',
      endDate: '',
      reason: '',
      isHalfDay: false,
      applyToAll: false,
      selectedEmployees: [],
      departmentFilter: [],
      designationFilter: [],
      autoApprove: false,
      notifyEmployees: true,
      attachments: []
    });
    setSearchTerm('');
    setDepartmentFilter('all');
    setDesignationFilter('all');
    setShowPreview(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Bulk Leave Management</h2>
              <p className="text-sm text-gray-500">Create leave requests for multiple employees</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {[
              { step: 1, title: 'Leave Details', icon: Calendar },
              { step: 2, title: 'Select Employees', icon: Users },
              { step: 3, title: 'Review & Submit', icon: CheckCircle }
            ].map(({ step, title, icon: Icon }) => (
              <div key={step} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  currentStep >= step 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {currentStep > step ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Icon className="h-4 w-4" />
                  )}
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  currentStep >= step ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {title}
                </span>
                {step < 3 && (
                  <div className={`ml-4 w-8 h-0.5 ${
                    currentStep > step ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {currentStep === 1 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Leave Request Details</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Leave Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Leave Type *
                  </label>
                  <select
                    value={bulkLeaveData.leaveType}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, leaveType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Leave Type</option>
                    {leaveTypes.map(lt => (
                      <option key={lt.id} value={lt.name}>
                        {lt.name} ({lt.maxDaysPerYear} days/year)
                      </option>
                    ))}
                  </select>
                </div>

                {/* Start Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date *
                  </label>
                  <input
                    type="date"
                    value={bulkLeaveData.startDate}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, startDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* End Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date *
                  </label>
                  <input
                    type="date"
                    value={bulkLeaveData.endDate}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, endDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Duration Display */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
                    {calculateDays(bulkLeaveData.startDate, bulkLeaveData.endDate, bulkLeaveData.isHalfDay)} days
                  </div>
                </div>
              </div>

              {/* Reason */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason *
                </label>
                <textarea
                  value={bulkLeaveData.reason}
                  onChange={(e) => setBulkLeaveData(prev => ({ ...prev, reason: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter reason for leave..."
                />
              </div>

              {/* Options */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isHalfDay"
                    checked={bulkLeaveData.isHalfDay}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, isHalfDay: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isHalfDay" className="ml-2 text-sm text-gray-700">
                    Half Day Leave
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoApprove"
                    checked={bulkLeaveData.autoApprove}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, autoApprove: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="autoApprove" className="ml-2 text-sm text-gray-700">
                    Auto-approve requests (Admin privilege)
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyEmployees"
                    checked={bulkLeaveData.notifyEmployees}
                    onChange={(e) => setBulkLeaveData(prev => ({ ...prev, notifyEmployees: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="notifyEmployees" className="ml-2 text-sm text-gray-700">
                    Send email notifications to employees
                  </label>
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Select Employees</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleSelectAll(true)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Select All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    onClick={() => handleSelectAll(false)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search employees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Department Filter */}
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <select
                    value={departmentFilter}
                    onChange={(e) => setDepartmentFilter(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Departments</option>
                    {departments.map(dept => (
                      <option key={dept} value={dept}>{dept}</option>
                    ))}
                  </select>
                </div>

                {/* Designation Filter */}
                <div className="relative">
                  <UserCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <select
                    value={designationFilter}
                    onChange={(e) => setDesignationFilter(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Designations</option>
                    {designations.map(designation => (
                      <option key={designation} value={designation}>{designation}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Employee List */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="max-h-96 overflow-y-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="px-4 py-3 text-left">
                          <input
                            type="checkbox"
                            checked={filteredEmployees.length > 0 && bulkLeaveData.selectedEmployees.length === filteredEmployees.length}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Employee</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Department</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Designation</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredEmployees.map(employee => (
                        <tr key={employee.id} className="hover:bg-gray-50">
                          <td className="px-4 py-3">
                            <input
                              type="checkbox"
                              checked={bulkLeaveData.selectedEmployees.includes(employee.id)}
                              onChange={(e) => handleEmployeeSelection(employee.id, e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <span className="text-xs font-medium text-gray-600">
                                  {employee.firstName[0]}{employee.lastName[0]}
                                </span>
                              </div>
                              <div className="ml-3">
                                <div className="text-sm font-medium text-gray-900">
                                  {employee.firstName} {employee.lastName}
                                </div>
                                <div className="text-sm text-gray-500">{employee.employeeId}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900">{employee.department}</td>
                          <td className="px-4 py-3 text-sm text-gray-900">{employee.designation}</td>
                          <td className="px-4 py-3 text-sm text-gray-500">{employee.officialEmail || employee.personalEmail || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {filteredEmployees.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No employees found matching your filters</p>
                </div>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Review & Submit</h3>

              {/* Leave Details Summary */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-3">Leave Request Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700 font-medium">Leave Type:</span>
                    <span className="ml-2 text-blue-900">{bulkLeaveData.leaveType}</span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Duration:</span>
                    <span className="ml-2 text-blue-900">
                      {calculateDays(bulkLeaveData.startDate, bulkLeaveData.endDate, bulkLeaveData.isHalfDay)} days
                    </span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Start Date:</span>
                    <span className="ml-2 text-blue-900">{new Date(bulkLeaveData.startDate).toLocaleDateString()}</span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">End Date:</span>
                    <span className="ml-2 text-blue-900">{new Date(bulkLeaveData.endDate).toLocaleDateString()}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-blue-700 font-medium">Reason:</span>
                    <span className="ml-2 text-blue-900">{bulkLeaveData.reason}</span>
                  </div>
                </div>
              </div>

              {/* Selected Employees Summary */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-3">
                  Selected Employees ({bulkLeaveData.selectedEmployees.length})
                </h4>
                <div className="max-h-48 overflow-y-auto">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {employees
                      .filter(emp => bulkLeaveData.selectedEmployees.includes(emp.id))
                      .map(employee => (
                        <div key={employee.id} className="flex items-center text-sm text-green-900">
                          <UserCheck className="h-4 w-4 text-green-600 mr-2" />
                          <span>{employee.firstName} {employee.lastName}</span>
                          <span className="ml-2 text-green-700">({employee.department})</span>
                        </div>
                      ))}
                  </div>
                </div>
              </div>

              {/* Options Summary */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Request Options</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <CheckCircle className={`h-4 w-4 mr-2 ${bulkLeaveData.isHalfDay ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className={bulkLeaveData.isHalfDay ? 'text-gray-900' : 'text-gray-500'}>
                      Half Day Leave
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className={`h-4 w-4 mr-2 ${bulkLeaveData.autoApprove ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className={bulkLeaveData.autoApprove ? 'text-gray-900' : 'text-gray-500'}>
                      Auto-approve requests
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className={`h-4 w-4 mr-2 ${bulkLeaveData.notifyEmployees ? 'text-green-600' : 'text-gray-400'}`} />
                    <span className={bulkLeaveData.notifyEmployees ? 'text-gray-900' : 'text-gray-500'}>
                      Send email notifications
                    </span>
                  </div>
                </div>
              </div>

              {/* Warning Messages */}
              {bulkLeaveData.autoApprove && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-yellow-900">Auto-Approval Warning</h4>
                      <p className="text-sm text-yellow-800 mt-1">
                        These leave requests will be automatically approved and will immediately affect employee leave balances.
                        This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            {currentStep === 2 && (
              <span>{bulkLeaveData.selectedEmployees.length} employees selected</span>
            )}
          </div>
          
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={() => setCurrentStep(prev => prev - 1)}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Previous
              </button>
            )}
            
            {currentStep < 3 ? (
              <button
                onClick={() => setCurrentStep(prev => prev + 1)}
                disabled={currentStep === 1 && (!bulkLeaveData.leaveType || !bulkLeaveData.startDate || !bulkLeaveData.endDate || !bulkLeaveData.reason.trim())}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isLoading || bulkLeaveData.selectedEmployees.length === 0}
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    <span>Create Leave Requests</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkLeaveManagementModal;
