import React, { useState, useEffect, useCallback, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation, Navigate, useNavigate, useParams } from 'react-router-dom';
import { Sidebar } from './components/Sidebar';
import { Header } from './components/Header';
import { Dashboard } from './components/Dashboard';
import { ServiceDesk } from './components/ServiceDesk';
import { LoginPage } from './components/LoginPage';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { SocketProvider } from './contexts/SocketContext';
import AssetManagement from './components/AssetManagement';
import { SoftwareManagement } from './components/SoftwareManagement';
import { NetworkManagement } from './components/NetworkManagement';
import { Infrastructure } from './components/Infrastructure';
import { CloudServices } from './components/CloudServices';
import { DatabaseManagement } from './components/DatabaseManagement';
import { DomainHosting } from './components/DomainHosting';
import { VendorManagement } from './components/VendorManagement';
import { ITProcurement } from './components/ITProcurement';
import { Security } from './components/Security';
import { SystemAdmin } from './components/SystemAdmin';
import { UserManagement } from './components/UserManagement';
import { ProfileSettings } from './components/ProfileSettings';
import KnowledgeBasePage from './components/KnowledgeBasePage';
import KnowledgeArticleView from './components/KnowledgeArticleView';
import KnowledgeArticleEditor from './components/KnowledgeArticleEditor';
import PrinterMaintenanceForm from './components/PrinterMaintenanceForm';
import PrinterMaintenance from './components/PrinterMaintenance';
import EmployeeManagementPage from './components/HR/employee/EmployeeManagementPage';
import EmployeeManagement from './components/HR/employee/EmployeeManagement';
import AddEmployeePage from './components/HR/employee/AddEmployeePage';
import AttendanceManagement from './components/HR/attendance/AttendanceManagement';
import PayrollManagement from './components/HR/payroll/PayrollManagement';
import LeaveManagement from './components/HR/leave/LeaveManagement';
import LeaveManagementRoutes from './routes/LeaveManagementRoutes';
import { X, Save, FileImage, Tag, Box, Cpu, Hash, Barcode, Calendar, MapPin, AlertTriangle, User, Truck, Shield, Image as ImageIcon, Server, Settings, Building2, ShoppingBag, Moon, Sun, Plus, Menu } from 'lucide-react';
import { Toaster } from 'react-hot-toast';
import AssetManagementDashboard from './components/AssetManagementDashboard';
import { ProtectedRoute } from './components/ProtectedRoute';
import { Register } from './components/Register';
import { toast } from 'react-hot-toast';
import { ExpenseProvider } from './contexts/ExpenseContext';
import { SoftwareLicenceForm } from './components/SoftwareLicenseForm';
import { EmailAccountManagement } from './components/EmailAccountManagement';
import ITBillingInvoicing from './components/ITBillingInvoicing';
import BudgetTracking from './components/BudgetTracking';
import BillingInvoiceForm from './components/BillingInvoiceForm';
import ITOperationLogPage from "./components/ITOperationLogPage";
import ITOperationLogForm from "./components/ITOperationLogForm";
import { Link } from 'react-router-dom';
import { AnimatedStat } from './components/AnimatedStat';
import EmployeePortal from './components/HR/employee/EmployeePortal';
import SimpleTest from './components/HR/Recruitment/SimpleTest';

interface AppContentProps {
  currentPage: string;
  setCurrentPage: (page: string) => void;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

function AppContent({ currentPage, setCurrentPage, sidebarOpen, setSidebarOpen }: AppContentProps) {
  const { isDarkMode, toggleDarkMode } = useTheme();
  const { user, loading, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const redirectedRef = useRef(false);
  const location = useLocation();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const previousPathRef = useRef(location.pathname);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const permissionCheckCompletedRef = useRef(false);
  
  // Get sidebar collapsed state from localStorage
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });
  
  // Function to toggle sidebar state - simplified for reliability
  const toggleSidebar = useCallback(() => {
    console.log('Toggle sidebar called, current state:', sidebarOpen);
    setSidebarOpen(!sidebarOpen);
  }, [sidebarOpen, setSidebarOpen]);

  // Track window width for responsive design
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Reset sidebar state ONLY when location actually changes (on mobile)
  useEffect(() => {
    // Only close sidebar if the path has actually changed
    if (previousPathRef.current !== location.pathname) {
      if (window.innerWidth < 1024 && sidebarOpen) {
        setSidebarOpen(false);
      }
      // Update the previous path
      previousPathRef.current = location.pathname;
      // Reset permission check when path changes
      permissionCheckCompletedRef.current = false;
    }
  }, [location.pathname, setSidebarOpen, sidebarOpen]);

  // Handle clicks outside the sidebar to close it on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarOpen &&
        window.innerWidth < 1024 &&
        sidebarRef.current && 
        !sidebarRef.current.contains(event.target as Node)
      ) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [sidebarOpen, setSidebarOpen]);

  // Auto-sync currentPage with location - memoized to prevent unnecessary renders
  const syncCurrentPage = useCallback(() => {
    // Extract page identifier from pathname
    const path = location.pathname.replace(/^\//, '');
    const pageParts = path.split('/');
    
    let targetPage = pageParts[0];
    
    // Handle HR sub-routes specifically
    if (pageParts[0] === 'hr' && pageParts[1]) {
      if (pageParts[1] === 'dashboard') {
        targetPage = 'hr-management';
      } else if (pageParts[1] === 'payroll') {
        targetPage = 'payroll-management';
      } else if (pageParts[1] === 'leave-management') {
        targetPage = 'leave-management';
      } else {
        targetPage = pageParts[1]; // Use the sub-route as the page (e.g., "employees", "attendance")
      }
    }
    // Handle Asset Management sub-routes
    else if (pageParts[0] === 'asset-management' && pageParts[1]) {
      if (pageParts[1] === 'hardware') {
        targetPage = 'hardware';
      } else if (pageParts[1] === 'software') {
        targetPage = 'software-management';
      } else if (pageParts[1] === 'mobile-devices') {
        targetPage = 'mobile-devices';
      } else if (pageParts[1] === 'network-devices') {
        targetPage = 'network-management';
      } else {
        targetPage = 'asset-management';
      }
    }
    // Handle Service Desk sub-routes
    else if (pageParts[0] === 'service-desk' && pageParts[1]) {
      if (pageParts[1] === 'create-ticket') {
        targetPage = 'create-ticket';
      } else if (pageParts[1] === 'tickets') {
        targetPage = 'tickets';
      } else if (pageParts[1] === 'knowledge') {
        targetPage = 'knowledge';
      } else {
        targetPage = 'service-desk';
      }
    }
    
    if (targetPage) {
      // Don't update if it's already set correctly
      if (currentPage !== targetPage) {
        console.log('Syncing currentPage with URL:', targetPage);
        setCurrentPage(targetPage);
      }
    }
  }, [location.pathname, currentPage, setCurrentPage]);

  useEffect(() => {
    syncCurrentPage();
  }, [syncCurrentPage]);

  // Check if user has permission to access the current page
  useEffect(() => {
    // Add a global variable to track if an access error has been shown
    if (!window.hasOwnProperty('accessErrorShown')) {
      (window as any).accessErrorShown = false;
    }

    // Skip if already checked for this path, or if user is not loaded yet, or if already redirected
    if (permissionCheckCompletedRef.current || !user || loading || redirectedRef.current || (window as any).accessErrorShown) {
      return;
    }

    // Mark as checked for this path to prevent multiple checks
    permissionCheckCompletedRef.current = true;

    const adminOnlyPages = ['system-admin', 'users', 'roles', 'system-settings', 'system-logs', 'company-profile'];
    const itStaffPages = ['asset-management', 'hardware', 'mobile-devices', 'network-devices', 
                         'software-management', 'network-management', 'infrastructure', 'servers', 
                         'storage', 'network', 'database-management', 'domain-hosting', 'cloud-services'];
    
    // Get the current path without leading slash for permission check
    const currentPath = location.pathname.replace(/^\//, '');
    
    // Explicitly allow all users to access tickets section
    const allowedForAllPages = ['dashboard', 'tickets', 'profile', 'knowledge'];
    
    // Skip permission check for pages that all users can access
    if (allowedForAllPages.includes(currentPage) || allowedForAllPages.includes(currentPath)) {
      return;
    }
    
    console.log('Checking permissions for:', { currentPage, currentPath, role: user.role });
    
    // Check if current page/path requires admin role
    const isAdminPage = adminOnlyPages.includes(currentPage) || adminOnlyPages.includes(currentPath);
    const userHasAdminRole = ['IT_ADMIN', 'ADMIN'].includes(user.role);
    
    if (isAdminPage && !userHasAdminRole) {
      console.log('Access denied: Admin page access attempted by non-admin');
      redirectedRef.current = true;
      (window as any).accessErrorShown = true;
      toast.error("You don't have permission to access this page");
      navigate('/dashboard');
      setCurrentPage('dashboard');
      return;
    }
    
    // Check if current page/path requires IT staff role
    const isStaffPage = itStaffPages.includes(currentPage) || itStaffPages.includes(currentPath);
    const userHasStaffRole = ['IT_ADMIN', 'IT_STAFF', 'ADMIN'].includes(user.role);
    
    if (isStaffPage && !userHasStaffRole) {
      console.log('Access denied: Staff page access attempted by non-staff');
      redirectedRef.current = true;
      (window as any).accessErrorShown = true;
      toast.error("You don't have permission to access this page");
      navigate('/dashboard');
      setCurrentPage('dashboard');
    }
  }, [currentPage, location.pathname, user, loading, navigate, setCurrentPage]);

  // Reset the redirect flag when changing paths
  useEffect(() => {
    redirectedRef.current = false;
  }, [location.pathname]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  const renderPage = () => {
    // Handle Dashboard route
    if (currentPage === 'dashboard') {
      return <Dashboard />;
    }
    
    // Specifically handle service-desk knowledge routes FIRST (more specific)
    if (location.pathname.startsWith('/service-desk/knowledge')) {
      console.log('Handling service-desk knowledge path:', location.pathname);
      
      // Check for article viewing
      if (location.pathname.includes('/articles/')) {
        const articleId = location.pathname.split('/').pop();
        console.log('Rendering service-desk article view with ID:', articleId);
        return <KnowledgeArticleView />;
      }
      
      // Check for article creation
      if (location.pathname.includes('/create')) {
        console.log('Rendering service-desk article editor (create)');
        return <KnowledgeArticleEditor />;
      }
      
      // Check for article editing
      if (location.pathname.includes('/edit/')) {
        console.log('🔧 APP TRACE: Rendering service-desk article editor (edit)');
        console.log('🔧 APP TRACE: Edit path detected:', location.pathname);
        return <KnowledgeArticleEditor />;
      }
      
      // Default to knowledge base page
      return <KnowledgeBasePage />;
    }
    
    // Handle other Service Desk routes - OPTIMIZED to prevent loops
    if (location.pathname.startsWith('/service-desk')) {
      // Map URL to appropriate view for ServiceDesk
      let serviceDeskView = 'service-desk-home';
      
      if (location.pathname === '/service-desk/create-ticket') {
        serviceDeskView = 'create-ticket';
      } else if (location.pathname === '/service-desk/tickets') {
        serviceDeskView = 'tickets';
      }
      
      return <ServiceDesk 
        view={serviceDeskView}
        onViewChange={(view) => setCurrentPage(view)}
      />;
    }
    
    // Handle Knowledge Base route
    if (currentPage === 'knowledge' || 
        currentPage === 'knowledge/create' || 
        currentPage === 'knowledge/edit' || 
        currentPage === 'knowledge-article') {
      console.log('Rendering knowledge component:', currentPage);
      
      // Check for article viewing using pathname
      if (location.pathname.includes('/articles/')) {
        const articleId = location.pathname.split('/').pop();
        console.log('Rendering article view with ID:', articleId);
        return <KnowledgeArticleView />;
      }
      
      if (currentPage === 'knowledge/create' || currentPage === 'knowledge/edit') {
          return <KnowledgeArticleEditor />;
      } else if (currentPage === 'knowledge-article') {
          return <KnowledgeArticleView />;
      } else {
          return <KnowledgeBasePage />;
      }
    }
    
    // Handle User Management route
    if (currentPage === 'user-management') {
      return <UserManagement />;
    }
    
    // Handle Asset Management routes
    if (currentPage === 'hardware-management' || 
        currentPage === 'hardware' ||
        currentPage === 'mobile-devices' || 
        currentPage === 'network-devices') {
      return <AssetManagement />;
    }

    // Handle Printer Maintenance routes
    if (currentPage === 'printer-maintenance') {
      return <PrinterMaintenance />;
    }
    
    // Handle Software Management route
    if (currentPage === 'software-management') {
      return <SoftwareManagement />;
    }
    
    // Handle Software License Form routes
    if (currentPage === 'software/add' || currentPage === 'software/edit') {
      return <SoftwareLicenceForm />;
    }

    // Handle Network Management route
    if (currentPage === 'network-management') {
      return <NetworkManagement />;
    }

    // Handle Infrastructure routes
    if (currentPage === 'infrastructure' ||
        currentPage === 'servers' ||
        currentPage === 'storage' ||
        currentPage === 'network') {
      return <Infrastructure />;
    }

    // Handle Database Management route
    if (currentPage === 'database-management' ||
        currentPage === 'database-instances' ||
        currentPage === 'backup-management' ||
        currentPage === 'performance-monitoring') {
      return <DatabaseManagement />;
    }

    // Handle Domain & Hosting routes
    if (currentPage === 'domain-hosting' ||
        currentPage === 'domain-management' ||
        currentPage === 'hosting-services' ||
        currentPage === 'ssl-certificates') {
      return <DomainHosting />;
    }

    // Handle Cloud Services route
    if (currentPage === 'cloud-services') {
      return <CloudServices />;
    }

    // Handle Security routes
    if (currentPage === 'security' ||
        currentPage === 'access-control' ||
        currentPage === 'security-monitoring' ||
        currentPage === 'incidents' ||
        currentPage === 'compliance') {
      return <Security />;
    }

    // Handle System Admin routes
    if (currentPage === 'system-admin' ||
        currentPage === 'users' ||
        currentPage === 'roles' ||
        currentPage === 'system-settings' ||
        currentPage === 'system-logs') {
      if (currentPage === 'users') {
        return <UserManagement />;
      }
      return <SystemAdmin defaultTab="users" />;
    }

    // Handle Vendor Management routes
    if (currentPage === 'vendor-management' ||
        currentPage === 'services' ||
        currentPage === 'contracts' ||
        currentPage === 'agreements' ||
        currentPage === 'performance' ||
        currentPage === 'contacts') {
      const section = currentPage === 'vendor-management' ? 'vendor-management' : currentPage;
      return <VendorManagement currentPage={section} />;
    }

    // Handle IT Procurement routes
    if (currentPage === 'it-procurement' ||
        currentPage === 'purchase-requests' ||
        currentPage === 'purchase-orders' ||
        currentPage === 'budget' ||
        currentPage === 'approvals') {
      return <ITProcurement />;
    }

    // Handle Email Management routes
    if (currentPage === 'email-management') {
      return <EmailAccountManagement />;
    }

    if (currentPage === 'create-email') {
      return <EmailAccountManagement />;
    }

    if (currentPage === 'email-templates') {
      return <EmailAccountManagement />;
    }

    if (currentPage === 'email-history') {
      return <EmailAccountManagement />;
    }

    if (currentPage === 'email-requests') {
      return <EmailAccountManagement />;
    }
    
    if (currentPage === 'email-accounts') {
      return <EmailAccountManagement />;
    }

    // Handle IT Billing & Invoicing routes
    if (currentPage === 'it-billing' || 
        currentPage === 'expenses' || 
        currentPage === 'billing' || 
        currentPage === 'invoices' ||
        currentPage === 'billing-invoice') {
      return <ITBillingInvoicing />;
    }

    // Handle Budget Tracking route
    if (currentPage === 'budget-tracking') {
      return <BudgetTracking />;
    }

    // Handle Add/Edit Invoice Form routes
    if (currentPage === 'add-invoice' || currentPage === 'edit-invoice') {
        return <BillingInvoiceForm />;
    }

    // Handle IT Operation Logs routes
    if (currentPage === 'it-operation-log' || location.pathname.startsWith('/it-logs')) {
      if (location.pathname === '/it-logs/new' || location.pathname.startsWith('/it-logs/edit/')) {
        return <ITOperationLogForm />;
      } else {
        return <ITOperationLogPage />;
      }
    }

    // Handle HR Management routes
    if (currentPage === 'employees') {
      return <EmployeeManagement />;
    }
    
    // Handle add/edit employee routes
    if (currentPage === 'add-employee' || location.pathname === '/hr/addemployee' || location.pathname.startsWith('/hr/addemployee/')) {
      // Extract employee ID from URL if in edit mode
      const employeeId = location.pathname.match(/\/hr\/addemployee\/(\d+)/)?.[1];
      return <AddEmployeePage employeeId={employeeId} />;
    }
    
    // Placeholder for future HR components (keep for other HR pages)
    if (currentPage === 'leaves' || currentPage === 'departments') {
      return (
        <div className="p-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 flex items-center justify-center">
            <p className="text-lg text-gray-500 dark:text-gray-400">This module is under development and will be available soon.</p>
          </div>
        </div>
      );
    }

    // Handle attendance page
    if (currentPage === 'attendance') {
      return <AttendanceManagement />;
    }

    // Handle leave management routes
    if (currentPage === 'leave-management' || currentPage === 'leave' || location.pathname.startsWith('/hr/leave-management')) {
      return <LeaveManagementRoutes />;
    }

    // Handle payroll management page
    if (currentPage === 'payroll-management') {
      return <PayrollManagement isAdmin={true} />;
    }

    // Handle recruitment page
    if (currentPage === 'recruitment') {
      return <SimpleTest />;
    }

    // Handle other routes
    switch (currentPage) {
      case 'assets':
        return <AssetManagementDashboard />;
      case 'hardware':
        return <AssetManagement />;
      case 'profile':
        return <ProfileSettings />;
      case 'settings':
        return <SystemAdmin defaultTab="settings" />;
      case 'employee-portal':
        return <EmployeePortal />;
      case 'company-profile':
        return <SystemAdmin defaultTab="company" />;
      default:
        return (
          <div className="p-6">
            <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
              <p className="text-gray-500">Content for {currentPage} will be displayed here</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen ${isDarkMode ? 'dark bg-gray-900 text-gray-100' : 'bg-gray-100 text-gray-900'}`}>
      {/* Main layout */}
      <div className="flex">
        {/* Sidebar - Fixed position on desktop */}
        <div className="hidden lg:block lg:fixed lg:inset-y-0 lg:left-0 lg:z-30">
          <Sidebar 
            currentPage={currentPage} 
            onPageChange={setCurrentPage} 
            isOpen={true}
            onClose={() => {}}
            onCollapsedChange={setSidebarCollapsed}
          />
        </div>

        {/* Mobile sidebar - Absolute position */}
        <div className="lg:hidden">
          {sidebarOpen && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 bg-gray-800/50 dark:bg-black/50 backdrop-blur-sm z-20"
                onClick={() => setSidebarOpen(false)}
                aria-hidden="true"
              />
              
              {/* Sidebar */}
              <div 
                ref={sidebarRef}
                className="fixed inset-y-0 left-0 z-30 bg-[#1a2234] dark:bg-gray-900 shadow-lg sidebar-container"
              >
                <Sidebar 
                  currentPage={currentPage} 
                  onPageChange={(page) => {
                    setCurrentPage(page);
                    setSidebarOpen(false);
                  }}
                  isOpen={true}
                  onClose={() => setSidebarOpen(false)}
                  onCollapsedChange={setSidebarCollapsed}
                />
              </div>
            </>
          )}
        </div>

        {/* Main content area */}
        <div 
          className={`flex-1 min-w-0 main-content transition-all duration-300 ease-in-out`}
          style={{ paddingLeft: windowWidth >= 1024 ? (sidebarCollapsed ? '4rem' : '16rem') : '0' }}
        >
          {/* Fixed header */}
          <Header 
            onMenuClick={toggleSidebar} 
            currentPage={currentPage}
            darkMode={isDarkMode}
            toggleDarkMode={toggleDarkMode}
            onPageChange={setCurrentPage}
            sidebarCollapsed={sidebarCollapsed}
          />
          
          {/* Main content with proper padding */}
          <main className="pt-16">
            {renderPage()}
          </main>
          
          {/* Footer */}
          <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  <span>© {new Date().getFullYear()} InfraSpine. All rights reserved.</span>
                  <span className="hidden sm:inline">|</span>
                  <span className="hidden sm:inline">v1.0.0</span>
                </div>
                <div className="flex items-center space-x-4 mt-2 sm:mt-0">
                  <span>Powered by InfraSpine Technology</span>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
}

// Role-based protected route component
interface RoleProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: string[];
  redirectPath?: string;
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({ 
  children, 
  allowedRoles,
  redirectPath = '/dashboard'
}) => {
  const { user, isAuthenticated, loading } = useAuth();
  
  // Reset the access error flag when successfully accessing a protected route
  useEffect(() => {
    if (isAuthenticated && !loading && user && allowedRoles.includes(user.role)) {
      // Reset the global access error flag
      if (window.hasOwnProperty('accessErrorShown')) {
        (window as any).accessErrorShown = false;
      }
    }
    
    // Cleanup function to ensure we reset the flag when component unmounts
    return () => {
      if (window.hasOwnProperty('accessErrorShown')) {
        (window as any).accessErrorShown = false;
      }
    };
  }, [isAuthenticated, loading, user, allowedRoles]);
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Reset the global access error flag before redirecting
    if (window.hasOwnProperty('accessErrorShown')) {
      (window as any).accessErrorShown = false;
    }
    return <Navigate to="/login" replace />;
  }

  // Check if an access error has already been shown
  if (!window.hasOwnProperty('accessErrorShown')) {
    (window as any).accessErrorShown = false;
  }

  // Check if user has the required role
  if (!user || !allowedRoles.includes(user.role)) {
    // Special case: If user has IT_ADMIN role, allow access regardless of specified roles
    if (user && user.role === 'IT_ADMIN') {
      return <>{children}</>;
    }
    
    if (!(window as any).accessErrorShown) {
      (window as any).accessErrorShown = true;
      toast.error("You don't have permission to access this page");
    }
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
};

function AuthenticatedApp() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { user, isAuthenticated, loading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const path = location.pathname.substring(1);
    const mainPath = path.split('/')[0];
    const subPath = path.split('/')[1];
    const idParam = path.split('/')[2]; // For edit routes
    
    console.log('Route changed:', { path, mainPath, subPath, idParam });
    
    // Handle HR Management paths
    if (mainPath === 'hr-management') {
      if (['employees', 'attendance', 'leaves', 'departments'].includes(subPath)) {
        setCurrentPage(subPath);
      } else {
        // Default to employees if subPath is missing or invalid under /hr-management
        navigate('/hr-management/employees', { replace: true }); 
        setCurrentPage('employees'); 
      }
      return; // Exit early
    }
    
    // Handle direct HR paths
    if (mainPath === 'hr') {
      if (subPath === 'employees') {
        setCurrentPage('employees');
      } else if (subPath === 'addemployee') {
        setCurrentPage('add-employee');
      } else if (subPath === 'attendance') {
        setCurrentPage('attendance');
      } else if (subPath === 'recruitment') {
        setCurrentPage('recruitment');
      } else {
        // Default to employees if subPath is missing or invalid under /hr
        navigate('/hr/employees', { replace: true });
        setCurrentPage('employees');
      }
      return; // Exit early
    }

    // Handle leave management routes
    if (mainPath === 'hr' && subPath === 'leave-management') {
      // Extract the specific leave management page from the URL
      const leavePage = path.split('/')[2]; // e.g., 'overview', 'allocations', etc.
      if (leavePage) {
        setCurrentPage(`leave-${leavePage}`); // e.g., 'leave-overview', 'leave-allocations'
      } else {
        setCurrentPage('leave-overview'); // Default to overview
      }
      return; // Exit early
    }
    
    // Handle old leave management for backward compatibility
    if (mainPath === 'leave-management') {
      setCurrentPage('leave-management');
      return; // Exit early
    }

    // Handle IT Billing & Expense paths including add/edit
    if (mainPath === 'it-billing') {
      if (subPath === 'add-invoice') {
        setCurrentPage('add-invoice');
      } else if (subPath === 'edit-invoice' && idParam) {
        setCurrentPage('edit-invoice'); 
      } else if (['expenses', 'billing', 'invoices', 'budget-tracking', 'billing-invoice'].includes(subPath)) {
        setCurrentPage(subPath);
      } else {
        // Default to billing-invoice if subPath is missing or invalid under /it-billing
        navigate('/it-billing/billing-invoice', { replace: true }); 
        setCurrentPage('billing-invoice'); 
      }
      return; // Exit early
    }
    
    // Handle Vendor Management paths
    if (mainPath === 'vendor-management') {
       // ... existing vendor logic ...
    }

    // Handle direct access to vendor sections
    if (['services', 'contracts', 'agreements', 'performance', 'contacts'].includes(mainPath)) {
       // ... existing vendor redirect logic ...
    }

    // REMOVE direct access redirects for billing/invoice as they are now handled above
    // if (['expenses', 'billing', 'invoices', 'budget-tracking', 'billing-invoice'].includes(mainPath)) { ... }

    // Handle Employee Portal path
    if (mainPath === 'employee-portal') {
      setCurrentPage('employee-portal');
      return; // Exit early
    }

    const pathToPageMap: Record<string, string> = {
      'dashboard': 'dashboard',
      'service-desk': 'service-desk',
      'tickets': 'tickets',
      'knowledge': 'knowledge',
      'create-ticket': 'create-ticket',
      'hr': 'employees', // Default HR page
      'hr/employees': 'employees',
      'hr/addemployee': 'add-employee',
      'hr/attendance': 'attendance',
      'hr/leave': 'leave',
      'hr/advanced-attendance': 'advanced-attendance',
      'hr/payroll': 'payroll-management',
      'hr/recruitment': 'recruitment',
      'hr-management': 'hr-management',
      'employees': 'employees',
      'attendance': 'attendance',
      'leaves': 'leaves',
      'departments': 'departments',

      'email-management': 'email-management',
      'assets': 'hardware',
      'assets/hardware': 'hardware',
      'assets/mobile': 'mobile-devices',
      'assets/network': 'network-devices',
      'assets/simple': 'simple-assets',
      'software': 'software-management',
      'software/add': 'software-management',
      'software/edit': 'software-management',
      'network': 'network-management',
      'users': 'users',
      'profile': 'profile',
      'printer-maintenance': 'printer-maintenance',
      'expenses': 'expenses',
      'billing': 'billing',
      'invoices': 'invoices',
      'budget-tracking': 'budget-tracking',
      'it-billing': 'billing-invoice', // Default to list view
      'add-invoice': 'add-invoice', // Add map entry
      'edit-invoice': 'edit-invoice', // Add map entry
      'vendor-management': 'vendor-management',
      'services': 'services',
      'contracts': 'contracts',
      'agreements': 'agreements',
      'performance': 'performance',
      'contacts': 'contacts',
      'create-email': 'create-email',
      'email-templates': 'email-templates',
      'email-history': 'email-history',
      'email-requests': 'email-requests',
      'email-accounts': 'email-accounts',
      'billing-invoice': 'billing-invoice',
      'it-logs': 'it-operation-log',
      'employee-portal': 'employee-portal', // Add employee portal mapping
      'system-logs': 'system-logs', // Add system logs mapping
    };

    // Update logic to use the map
    if (pathToPageMap[mainPath]) {
        setCurrentPage(pathToPageMap[mainPath]);
    } else if (mainPath === 'knowledge') {
        // Handle knowledge routes
        if (subPath === 'create') {
            setCurrentPage('knowledge/create');
        } else if (subPath === 'edit') {
            setCurrentPage('knowledge/edit');
        } else if (subPath === 'articles') {
            setCurrentPage('knowledge-article');
        } else {
            setCurrentPage('knowledge');
        }
    } else if (mainPath === 'service-desk') {
        // Handle service-desk routes for sidebar highlighting
        if (subPath === 'create-ticket') {
            setCurrentPage('create-ticket');
        } else if (subPath === 'tickets') {
            setCurrentPage('tickets');
        } else if (subPath === 'knowledge') {
            if (path.split('/')[2] === 'create') {
                setCurrentPage('knowledge');
            } else if (path.split('/')[2] === 'edit') {
                setCurrentPage('knowledge');
            } else if (path.split('/')[2] === 'articles') {
                setCurrentPage('knowledge');
            } else {
                setCurrentPage('knowledge');
            }
        } else {
            setCurrentPage('service-desk');
        }
    } else if (mainPath === 'assets') {
         // ... existing assets logic ...
    } else {
      // Fallback or default page if no match
      // Consider navigating to dashboard or showing a 404
       setCurrentPage('dashboard'); // Example fallback
    }

  }, [location.pathname, navigate]);

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<Register />} />
      
      {/* Protected routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <AppContent 
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
          />
        </ProtectedRoute>
      }>
        <Route index element={<Navigate to="/dashboard" replace />} />
        
        {/* Dashboard */}
        <Route path="dashboard" element={<Dashboard />} />
        
        {/* Service Desk routes - UNIFIED to prevent multiple instances */}
        <Route path="service-desk/*" element={
          <ServiceDesk 
            view={currentPage} 
            onViewChange={(view) => setCurrentPage(view)}
          />
        } />
        
        {/* Legacy ticket routes - redirect to new paths */}
        <Route path="tickets" element={<Navigate to="/service-desk/tickets" replace />} />
        <Route path="tickets/create" element={<Navigate to="/service-desk/create-ticket" replace />} />
        <Route path="tickets/:id" element={
          <ServiceDesk 
            view="ticket-detail" 
            onViewChange={(view) => setCurrentPage(view)}
          />
        } />
        
        {/* Knowledge routes - direct access */}
        <Route path="knowledge" element={<KnowledgeBasePage />} />
        <Route path="knowledge/articles/:id" element={<KnowledgeArticleView />} />
        <Route path="knowledge/create" element={
          <React.Suspense fallback={<div>Loading Knowledge Base Editor...</div>}>
            <KnowledgeArticleEditor />
          </React.Suspense>
        } />
        <Route path="knowledge/edit/:id" element={<KnowledgeArticleEditor />} />
        
        {/* Service Desk Knowledge routes */}
        <Route path="service-desk/knowledge/articles/:id" element={<KnowledgeArticleView />} />
        <Route path="service-desk/knowledge/create" element={
          <React.Suspense fallback={<div>Loading Knowledge Base Editor...</div>}>
            <KnowledgeArticleEditor />
          </React.Suspense>
        } />
        <Route path="service-desk/knowledge/edit/:id" element={<KnowledgeArticleEditor />} />
        
        {/* User Management routes */}
        <Route path="user-management" element={<UserManagement />} />
        
        {/* Asset Management routes */}
        <Route path="hardware-management" element={<AssetManagement />} />
        <Route path="hardware" element={<AssetManagement />} />
        <Route path="mobile-devices" element={<AssetManagement />} />
        <Route path="network-devices" element={<AssetManagement />} />

      {/* Printer Maintenance routes */}
        <Route path="printer-maintenance" element={<PrinterMaintenance />} />
        
        {/* Software Management routes */}
        <Route path="software-management" element={<SoftwareManagement />} />
        
        {/* Software License Form routes */}
        <Route path="software/add" element={<SoftwareLicenceForm />} />
        <Route path="software/edit/:id" element={<SoftwareLicenceForm />} />
        
        {/* Network Management routes */}
        <Route path="network-management" element={<NetworkManagement />} />
        
        {/* Infrastructure routes */}
        <Route path="infrastructure" element={<Infrastructure />} />
        <Route path="servers" element={<Infrastructure />} />
        <Route path="storage" element={<Infrastructure />} />
        <Route path="network" element={<Infrastructure />} />
        
        {/* Database Management routes */}
        <Route path="database-management" element={<DatabaseManagement />} />
        <Route path="database-instances" element={<DatabaseManagement />} />
        <Route path="backup-management" element={<DatabaseManagement />} />
        <Route path="performance-monitoring" element={<DatabaseManagement />} />
        
        {/* Domain & Hosting routes */}
        <Route path="domain-hosting" element={<DomainHosting />} />
        <Route path="domain-management" element={<DomainHosting />} />
        <Route path="hosting-services" element={<DomainHosting />} />
        <Route path="ssl-certificates" element={<DomainHosting />} />
        
        {/* Cloud Services routes */}
        <Route path="cloud-services" element={<CloudServices />} />
        
        {/* Security routes */}
        <Route path="security" element={<Security />} />
        <Route path="access-control" element={<Security />} />
        <Route path="security-monitoring" element={<Security />} />
        <Route path="incidents" element={<Security />} />
        <Route path="compliance" element={<Security />} />
        
        {/* System Admin routes */}
        <Route path="system-admin" element={<SystemAdmin defaultTab="users" />} />
        <Route path="users" element={<SystemAdmin defaultTab="users" />} />
        <Route path="roles" element={<SystemAdmin defaultTab="roles" />} />
        <Route path="system-settings" element={<SystemAdmin defaultTab="settings" />} />
        <Route path="system-logs" element={<SystemAdmin defaultTab="logs" />} />
        <Route path="company-profile" element={<SystemAdmin defaultTab="company" />} />
        
        {/* Vendor Management routes */}
        <Route path="vendor-management" element={<VendorManagement currentPage="vendor-management" />} />
        <Route path="services" element={<VendorManagement currentPage="services" />} />
        <Route path="contracts" element={<VendorManagement currentPage="contracts" />} />
        <Route path="agreements" element={<VendorManagement currentPage="agreements" />} />
        <Route path="performance" element={<VendorManagement currentPage="performance" />} />
        <Route path="contacts" element={<VendorManagement currentPage="contacts" />} />
        <Route path="vendor-management/:section" element={<VendorManagementWrapper />} />
        
        {/* IT Procurement routes */}
        <Route path="it-procurement" element={<ITProcurement />} />
        <Route path="purchase-requests" element={<ITProcurement />} />
        <Route path="purchase-orders" element={<ITProcurement />} />
        <Route path="budget" element={<ITProcurement />} />
        <Route path="approvals" element={<ITProcurement />} />
        
        {/* Email Management routes */}
        <Route path="email-management" element={<EmailAccountManagement />} />
        <Route path="create-email" element={<EmailAccountManagement />} />
        <Route path="email-templates" element={<EmailAccountManagement />} />
        <Route path="email-history" element={<EmailAccountManagement />} />
        <Route path="email-requests" element={<EmailAccountManagement />} />
        <Route path="email-accounts" element={<EmailAccountManagement />} />
        
        {/* IT Billing & Expense routes */}
        <Route path="it-billing" element={<ITBillingInvoicing />} />
        <Route path="expenses" element={<ITBillingInvoicing />} />
        <Route path="billing" element={<ITBillingInvoicing />} />
        <Route path="invoices" element={<ITBillingInvoicing />} />
        <Route path="billing-invoice" element={<ITBillingInvoicing />} />
        <Route path="budget-tracking" element={<BudgetTracking />} />
        
        {/* IT Operation Logs routes */}
        <Route path="it-logs" element={<ITOperationLogPage />} />
        <Route path="it-logs/new" element={<ITOperationLogForm />} />
        <Route path="it-logs/edit/:id" element={<ITOperationLogForm />} />
        
        {/* HR Management routes */}
        <Route path="hr-management" element={<EmployeeManagementPage />} />
        <Route path="hr-management/employees" element={<EmployeeManagementPage />} />
        <Route path="hr-management/attendance" element={<AttendanceManagement />} />
        <Route path="hr-management/leaves" element={<EmployeeManagementPage />} />
        <Route path="hr-management/departments" element={<EmployeeManagementPage />} />
        
        {/* Add a route for direct access to the HR Employees page */}
        <Route path="hr/employees" element={<EmployeeManagement />} />
        
        {/* Add a route for the Add Employee page */}
        <Route path="hr/addemployee" element={<AddEmployeePage />} />
        <Route path="hr/addemployee/:id" element={<EmployeeDetailWrapper />} />
        <Route path="hr/employee/:id" element={<EmployeeDetailWrapper />} />
        <Route path="hr/attendance" element={<AttendanceManagement />} />
        <Route path="hr/leave-management/*" element={<LeaveManagementRoutes />} />
        <Route path="hr/payroll" element={<PayrollManagement isAdmin={true} />} />

        {/* Employee Portal route */}
                        <Route path="employee-portal" element={<EmployeePortal />} />

      {/* Default route */}
        <Route path="*" element={<AppContent currentPage={currentPage} setCurrentPage={setCurrentPage} sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />} />
      </Route>
    </Routes>
  );
}

// VendorManagementWrapper component to extract URL params
function VendorManagementWrapper() {
  const { section } = useParams<{ section: string }>();
  return <VendorManagement currentPage={section || 'vendor-management'} />;
}

// EmployeeDetailWrapper component to extract URL params
function EmployeeDetailWrapper() {
  const { id } = useParams<{ id: string }>();
  return <AddEmployeePage employeeId={id} />;
}

export default function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <SocketProvider>
            <ExpenseProvider>
              <Toaster position="top-right" />
              <Routes>
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<Register />} />
                <Route path="*" element={
                  <ProtectedRoute>
                    <AppContent 
                      currentPage={currentPage} 
                      setCurrentPage={setCurrentPage}
                      sidebarOpen={sidebarOpen}
                      setSidebarOpen={setSidebarOpen}
                    />
                  </ProtectedRoute>
                } />
              </Routes>
            </ExpenseProvider>
          </SocketProvider>
        </AuthProvider>
      </ThemeProvider>
    </Router>
  );
}
