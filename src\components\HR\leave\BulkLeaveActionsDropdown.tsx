import React, { useState } from 'react';
import { 
  ChevronDown, 
  Users, 
  Download, 
  Upload, 
  FileText, 
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Settings
} from 'lucide-react';

interface BulkLeaveActionsDropdownProps {
  onBulkCreate: () => void;
  onBulkApprove: () => void;
  onBulkReject: () => void;
  onExportData: () => void;
  onImportData: () => void;
  onViewHistory: () => void;
  selectedCount?: number;
}

const BulkLeaveActionsDropdown: React.FC<BulkLeaveActionsDropdownProps> = ({
  onBulkCreate,
  onBulkApprove,
  onBulkReject,
  onExportData,
  onImportData,
  onViewHistory,
  selectedCount = 0
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const actions = [
    {
      id: 'create',
      label: 'Create Bulk Leave',
      icon: Users,
      onClick: onBulkCreate,
      description: 'Create leave requests for multiple employees',
      color: 'text-blue-600 hover:bg-blue-50'
    },
    {
      id: 'approve',
      label: `Approve Selected (${selectedCount})`,
      icon: CheckCircle,
      onClick: onBulkApprove,
      description: 'Approve selected leave requests',
      color: 'text-green-600 hover:bg-green-50',
      disabled: selectedCount === 0
    },
    {
      id: 'reject',
      label: `Reject Selected (${selectedCount})`,
      icon: XCircle,
      onClick: onBulkReject,
      description: 'Reject selected leave requests',
      color: 'text-red-600 hover:bg-red-50',
      disabled: selectedCount === 0
    },
    {
      id: 'divider1',
      type: 'divider'
    },
    {
      id: 'export',
      label: 'Export Leave Data',
      icon: Download,
      onClick: onExportData,
      description: 'Export leave data to Excel/CSV',
      color: 'text-gray-600 hover:bg-gray-50'
    },
    {
      id: 'import',
      label: 'Import Leave Data',
      icon: Upload,
      onClick: onImportData,
      description: 'Import leave data from Excel/CSV',
      color: 'text-gray-600 hover:bg-gray-50'
    },
    {
      id: 'divider2',
      type: 'divider'
    },
    {
      id: 'history',
      label: 'View Bulk Operations',
      icon: Clock,
      onClick: onViewHistory,
      description: 'View history of bulk operations',
      color: 'text-gray-600 hover:bg-gray-50'
    }
  ];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <Settings className="h-4 w-4 mr-2" />
        Bulk Actions
        <ChevronDown className="h-4 w-4 ml-2" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20">
            <div className="py-1">
              {actions.map((action) => {
                if (action.type === 'divider') {
                  return (
                    <div key={action.id} className="border-t border-gray-100 my-1" />
                  );
                }

                const Icon = action.icon!;
                return (
                  <button
                    key={action.id}
                    onClick={() => {
                      if (!action.disabled) {
                        action.onClick();
                        setIsOpen(false);
                      }
                    }}
                    disabled={action.disabled}
                    className={`w-full text-left px-4 py-3 text-sm transition-colors ${
                      action.disabled 
                        ? 'text-gray-400 cursor-not-allowed' 
                        : action.color
                    }`}
                  >
                    <div className="flex items-start">
                      <Icon className="h-5 w-5 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <div className="font-medium">{action.label}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          {action.description}
                        </div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default BulkLeaveActionsDropdown;
