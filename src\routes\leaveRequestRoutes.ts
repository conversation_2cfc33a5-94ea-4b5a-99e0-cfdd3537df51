import { Router } from 'express';
import { LeaveRequestController } from '../controllers/leaveRequestController';
import { requireAuth } from '../middleware/auth';

const router = Router();
const leaveRequestController = new LeaveRequestController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Get all leave requests with filtering and pagination
router.get('/', leaveRequestController.getAll.bind(leaveRequestController));

// Get leave request by ID
router.get('/:id', leaveRequestController.getById.bind(leaveRequestController));

// Create new leave request
router.post('/', leaveRequestController.create.bind(leaveRequestController));

// Approve leave request
router.post('/:id/approve', leaveRequestController.approve.bind(leaveRequestController));

// Reject leave request
router.post('/:id/reject', leaveRequestController.reject.bind(leaveRequestController));

// Cancel leave request
router.post('/:id/cancel', leaveRequestController.cancel.bind(leaveRequestController));

// Bulk operations
router.post('/bulk/validate', leaveRequestController.validateBulkLeave.bind(leaveRequestController));
router.post('/bulk/create', leaveRequestController.createBulkLeave.bind(leaveRequestController));

export default router;