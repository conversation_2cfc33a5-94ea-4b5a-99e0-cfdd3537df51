import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './User';
import { LeaveStatus } from '../types/attendance';

@Entity('leave_requests')
export class LeaveRequest {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  employeeId: number;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'employeeId' })
  employee: User;

  @Column({ type: 'varchar', length: 50 })
  leaveType: string; // Dynamic leave type

  @Column({ type: 'date' })
  startDate: string;

  @Column({ type: 'date' })
  endDate: string;

  @Column({ type: 'int' })
  daysRequested: number;

  @Column({
    type: 'enum',
    enum: LeaveStatus,
    default: LeaveStatus.PENDING
  })
  status: LeaveStatus;

  @Column({ type: 'text' })
  reason: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  approverId?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approverId' })
  approver?: User;

  @Column({ type: 'varchar', length: 255, nullable: true })
  approverName?: string;

  @Column({ type: 'text', nullable: true })
  approverComments?: string;

  @Column({ type: 'datetime', nullable: true })
  approvedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  rejectedAt?: Date;

  @Column({ type: 'text', nullable: true })
  attachments?: string; // JSON string of file paths

  @Column({ type: 'boolean', default: false })
  isEmergencyLeave: boolean;

  @Column({ type: 'text', nullable: true })
  emergencyContact?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  delegatedTo?: string;

  @Column({ type: 'text', nullable: true })
  handoverNotes?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties for calculated fields
  get duration(): number {
    const start = new Date(this.startDate);
    const end = new Date(this.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }

  get isPending(): boolean {
    return this.status === LeaveStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.status === LeaveStatus.APPROVED;
  }

  get isRejected(): boolean {
    return this.status === LeaveStatus.REJECTED;
  }
} 