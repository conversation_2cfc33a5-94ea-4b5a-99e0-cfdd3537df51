import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAttendanceRegularizationRequestsTable1710000000100 implements MigrationInterface {
  name = 'CreateAttendanceRegularizationRequestsTable1710000000100';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create table (MySQL syntax)
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS attendance_regularization_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        employee_name VARCHAR(255) NOT NULL,
        date DATE NOT NULL,
        type VARCHAR(50) NOT NULL,
        requested_time TIME,
        reason TEXT NOT NULL,
        status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending',
        approver_comments TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_regularization_employee FOREIG<PERSON> KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
      ) ENGINE=InnoDB;
    `);

    // Helpful indexes for faster filtering
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS idx_regularization_employee_id ON attendance_regularization_requests(employee_id);`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS idx_regularization_status ON attendance_regularization_requests(status);`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS idx_regularization_date ON attendance_regularization_requests(date);`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS attendance_regularization_requests;`);
  }
} 