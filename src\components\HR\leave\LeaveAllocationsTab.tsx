import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Download,
  Settings,
  Edit3,
  Users,
  Plus,
  Minus,
  Save,
  X,
  AlertCircle,
  Filter,
  Check,
  ChevronDown,
  User
} from 'lucide-react';
import { leaveBalancesApi } from '../../../services/leaveApi';
import { leaveTypesApi } from '../../../services/leavePolicyApi';

interface LeaveType {
  id: number;
  leaveType: string;
  displayName: string;
  maxDaysPerYear: number;
}

interface Employee {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveAllocations: { [leaveTypeKey: string]: number };
}

interface LeaveAllocationsTabProps {
  departments: string[];
  onNotification: (notification: { type: 'success' | 'error' | 'warning'; message: string }) => void;
}

interface AdjustmentModal {
  isOpen: boolean;
  type: 'individual' | 'bulk';
  employee?: Employee;
  leaveType?: string;
  currentValue?: number;
}

interface BulkFilters {
  selectedEmployees: number[];
  selectedDepartments: string[];
  selectAll: boolean;
}

const LeaveAllocationsTab: React.FC<LeaveAllocationsTabProps> = ({
  departments,
  onNotification
}) => {
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const loadingRef = useRef(false);
  const mountedRef = useRef(true);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalEmployees, setTotalEmployees] = useState(0);

  // Adjustment modal states
  const [adjustmentModal, setAdjustmentModal] = useState<AdjustmentModal>({
    isOpen: false,
    type: 'individual'
  });
  const [adjustmentValue, setAdjustmentValue] = useState<number>(0);
  const [adjustmentReason, setAdjustmentReason] = useState<string>('');
  const [bulkAdjustments, setBulkAdjustments] = useState<{[leaveType: string]: number}>({});
  const [adjustmentMode, setAdjustmentMode] = useState<'set' | 'add' | 'subtract'>('set');
  
  // Bulk filters state
  const [bulkFilters, setBulkFilters] = useState<BulkFilters>({
    selectedEmployees: [],
    selectedDepartments: [],
    selectAll: true
  });
  const [showEmployeeDropdown, setShowEmployeeDropdown] = useState(false);
  const [showDepartmentDropdown, setShowDepartmentDropdown] = useState(false);



  // Individual employee adjustment modal
  const [showIndividualModal, setShowIndividualModal] = useState(false);
  const [selectedEmployeeForAdjustment, setSelectedEmployeeForAdjustment] = useState<Employee | null>(null);
  const [tempValues, setTempValues] = useState<{[leaveType: string]: number}>({});
  const [savingAllChanges, setSavingAllChanges] = useState(false);

  // Load leave types from policy configuration ONLY - no duplicates
  const loadLeaveTypes = async () => {
    try {
      // Use direct import instead of dynamic import to avoid promise issues
      const policyResponse = await leaveTypesApi.getActive();
      
      if (policyResponse.success && policyResponse.data) {
        const policyLeaveTypes = policyResponse.data.filter((lt: any) => lt.enabled && lt.isActive);
        
        const types = policyLeaveTypes.map((lt: any) => ({
          id: lt.id || Date.now() + Math.random(), // Ensure unique ID
          leaveType: lt.leaveType,
          displayName: lt.displayName || lt.leaveType,
          maxDaysPerYear: lt.maxDaysPerYear || 0
        }));
        
        if (mountedRef.current) {
          setLeaveTypes(types);
          
          if (types.length === 0) {
            onNotification({ 
              type: 'warning', 
              message: 'No active leave types found. Please configure leave types first.' 
            });
          }
        }
        
        return types.length > 0; // Return boolean indicating success
      } else {
        console.warn('❌ No active leave types found');
        setLeaveTypes([]);
        onNotification({ 
          type: 'warning', 
          message: 'No leave types found. Please configure leave types in the Policy tab first.' 
        });
        return false; // Return false if no leave types found
      }
    } catch (error) {
      console.error('❌ Error loading leave types:', error);
      setLeaveTypes([]);
      onNotification({ type: 'error', message: 'Failed to load leave types' });
      return false; // Return false on error
    }
  };

  // Load employees with their leave allocations
  const loadEmployees = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (loadingRef.current) {
      console.log('🚫 Load already in progress, skipping...');
      return Promise.resolve(); // Return resolved promise
    }
    
    try {
      loadingRef.current = true;
      setLoading(true);
      console.log('🔍 Loading employees with leave allocations...');
      
      // Clear previous data to prevent confusion
      setEmployees([]);
      
      // Start with smaller batch for faster initial load
      const initialLimit = 50; // Reduced from 1000 to 50 for faster loading
      const response = await leaveBalancesApi.getAllActiveEmployeesWithBalances(
        new Date().getFullYear(),
        departmentFilter === 'all' ? undefined : departmentFilter,
        1,
        initialLimit,
        search
      );

      if (response.success && response.data) {
        const employeeData = Array.isArray(response.data) ? response.data : [];
        console.log('🔍 Raw employee data count:', employeeData.length);
        
        // Handle pagination info if available
        const responseWithPagination = response as any;
        if (responseWithPagination.pagination) {
          setTotalEmployees(responseWithPagination.pagination.total);
          setHasMoreData(responseWithPagination.pagination.page < responseWithPagination.pagination.totalPages);
        } else {
          setTotalEmployees(employeeData.length);
          setHasMoreData(employeeData.length >= initialLimit);
        }
        
        const processedEmployees = employeeData.map(emp => {
          const leaveAllocations: { [leaveTypeKey: string]: number } = {};
          
          // Process leave balances - try different possible structures
          const balances = emp.balances || emp.leaveBalances || [];
          
          balances.forEach((balance: any) => {
            // Try to match by leaveType field
            const leaveTypeKey = balance.leaveType;
            if (leaveTypeKey) {
              // Use totalAllocated directly, not total (which includes carriedForward)
              leaveAllocations[leaveTypeKey] = balance.totalAllocated || 0;
            }
          });



          return {
            employeeId: emp.employeeId, // Backend sends database ID as employeeId
            employeeName: emp.employeeName || emp.name || 'Unknown',
            employeeCode: emp.employeeCode || 'N/A',
            department: emp.department || 'Unknown',
            position: emp.position || emp.designation || 'Unknown',
            leaveAllocations
          };
        });

        // Remove duplicates based on employeeId AND employeeName to prevent duplicate rows
        const uniqueEmployees = processedEmployees.filter((employee, index, self) => 
          index === self.findIndex(e => 
            e.employeeId === employee.employeeId && 
            e.employeeName === employee.employeeName
          )
        );

        console.log('🔍 Original employees count:', processedEmployees.length);
        console.log('🔍 Unique employees count:', uniqueEmployees.length);
        console.log('🔍 Unique employees:', uniqueEmployees.map(e => ({ id: e.employeeId, name: e.employeeName })));

        if (mountedRef.current) {
          setEmployees(uniqueEmployees);
          setCurrentPage(1); // Reset pagination when data changes
          
          // Reset bulk filters when employees change
          setBulkFilters({
            selectedEmployees: uniqueEmployees.map(emp => emp.employeeId),
            selectedDepartments: [],
            selectAll: true
          });
        }


      } else {
        console.error('❌ Failed to load employees:', response.message);
        if (mountedRef.current) {
          onNotification({ type: 'error', message: 'Failed to load employee data' });
        }
      }
    } catch (error) {
      console.error('❌ Error loading employees:', error);
      if (mountedRef.current) {
        onNotification({ type: 'error', message: 'Failed to load employee data' });
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
      loadingRef.current = false;
    }
  }, [departmentFilter, search, onNotification]);

  // Load more employees for pagination
  const loadMoreEmployees = useCallback(async () => {
    if (loadingRef.current || !hasMoreData) return Promise.resolve(); // Return resolved promise

    try {
      loadingRef.current = true;
      setLoading(true);

      const nextPage = Math.floor(employees.length / 50) + 1;
      const response = await leaveBalancesApi.getAllActiveEmployeesWithBalances(
        new Date().getFullYear(),
        departmentFilter === 'all' ? undefined : departmentFilter,
        nextPage,
        50,
        search
      );

      if (response.success && response.data) {
        const newEmployeeData = Array.isArray(response.data) ? response.data : [];
        
        // Handle pagination info
        const responseWithPagination = response as any;
        if (responseWithPagination.pagination) {
          setHasMoreData(responseWithPagination.pagination.page < responseWithPagination.pagination.totalPages);
        } else {
          setHasMoreData(false);
        }

        // Process and append new employees
        const processedNewEmployees = newEmployeeData.map(emp => {
          const leaveAllocations: { [leaveTypeKey: string]: number } = {};
          
          const balances = emp.balances || emp.leaveBalances || [];
          balances.forEach((balance: any) => {
            const leaveTypeKey = balance.leaveType;
            if (leaveTypeKey) {
              leaveAllocations[leaveTypeKey] = balance.totalAllocated || 0;
            }
          });

          return {
            employeeId: emp.employeeId,
            employeeName: emp.employeeName || emp.name || 'Unknown',
            employeeCode: emp.employeeCode || 'N/A',
            department: emp.department || 'Unknown',
            position: emp.position || emp.designation || 'Unknown',
            leaveAllocations
          };
        });

        // Filter out duplicates and append new employees
        const existingIds = new Set(employees.map(emp => emp.employeeId));
        const uniqueNewEmployees = processedNewEmployees.filter(emp => !existingIds.has(emp.employeeId));

        setEmployees(prevEmployees => [...prevEmployees, ...uniqueNewEmployees]);
      }
    } catch (error) {
      console.error('❌ Error loading more employees:', error);
      onNotification({ type: 'error', message: 'Failed to load more employees' });
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [employees, hasMoreData, departmentFilter, search, onNotification]);

  // Pagination calculations with memoization
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(employees.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentEmployees = employees.slice(startIndex, endIndex);
    
    return {
      totalPages,
      startIndex,
      endIndex,
      currentEmployees
    };
  }, [employees, itemsPerPage, currentPage]);

  const { totalPages, startIndex, endIndex, currentEmployees } = paginationData;

  // Handle individual adjustment
  const handleIndividualAdjustment = (employee: Employee, leaveType: string, currentValue: number) => {
    setAdjustmentModal({
      isOpen: true,
      type: 'individual',
      employee,
      leaveType,
      currentValue
    });
    setAdjustmentValue(currentValue);
    setAdjustmentReason('');
  };

  // Handle individual employee actions
  const handleEmployeeActions = (employee: Employee) => {
    setSelectedEmployeeForAdjustment(employee);
    setShowIndividualModal(true);
  };

  // Handle bulk adjustment modal
  const handleBulkAdjustment = () => {
    setAdjustmentModal({
      isOpen: true,
      type: 'bulk'
    });
    setShowEmployeeDropdown(false);
    setShowDepartmentDropdown(false);
    
    // Initialize with current average values for better UX
    const initialValues: { [key: string]: number } = {};
    leaveTypes.forEach(leaveType => {
      const stats = getCurrentLeaveStats(leaveType.leaveType);
      initialValues[leaveType.leaveType] = stats.avg;
    });
    setBulkAdjustments(initialValues);
  };

  // Toggle select all employees
  const toggleSelectAllEmployees = () => {
    if (bulkFilters.selectAll) {
      setBulkFilters(prev => ({
        ...prev,
        selectedEmployees: [],
        selectAll: false
      }));
    } else {
      setBulkFilters(prev => ({
        ...prev,
        selectedEmployees: employees.map(emp => emp.employeeId),
        selectAll: true
      }));
    }
  };

  // Toggle individual employee selection
  const toggleEmployeeSelection = (employeeId: number) => {
    setBulkFilters(prev => {
      const isSelected = prev.selectedEmployees.includes(employeeId);
      const newSelected = isSelected 
        ? prev.selectedEmployees.filter(id => id !== employeeId)
        : [...prev.selectedEmployees, employeeId];
      
      return {
        ...prev,
        selectedEmployees: newSelected,
        selectAll: newSelected.length === employees.length
      };
    });
  };

  // Toggle department selection
  const toggleDepartmentSelection = (department: string) => {
    setBulkFilters(prev => {
      const isSelected = prev.selectedDepartments.includes(department);
      const newSelected = isSelected 
        ? prev.selectedDepartments.filter(dept => dept !== department)
        : [...prev.selectedDepartments, department];
      
      // Auto-select/deselect employees in the department
      const departmentEmployees = employees
        .filter(emp => department === 'all' || emp.department === department)
        .map(emp => emp.employeeId);
      
      let updatedEmployees = [...prev.selectedEmployees];
      
      if (isSelected) {
        // Remove employees from this department
        updatedEmployees = updatedEmployees.filter(id => !departmentEmployees.includes(id));
      } else {
        // Add employees from this department
        departmentEmployees.forEach(id => {
          if (!updatedEmployees.includes(id)) {
            updatedEmployees.push(id);
          }
        });
      }
      
      return {
        ...prev,
        selectedDepartments: newSelected,
        selectedEmployees: updatedEmployees,
        selectAll: updatedEmployees.length === employees.length
      };
    });
  };

  // Get filtered employees for bulk adjustment
  const getFilteredEmployeesForBulk = () => {
    if (bulkFilters.selectAll) {
      return employees;
    }
    
    return employees.filter(emp => 
      bulkFilters.selectedEmployees.includes(emp.employeeId) ||
      bulkFilters.selectedDepartments.includes(emp.department)
    );
  };

  // Calculate current leave allocation statistics for selected employees
  const getCurrentLeaveStats = (leaveType: string) => {
    const filteredEmployees = getFilteredEmployeesForBulk();
    if (filteredEmployees.length === 0) return { min: 0, max: 0, avg: 0, current: 0 };

    // Find the leave type configuration to get maxDaysPerYear
    const leaveTypeConfig = leaveTypes.find(lt => lt.leaveType === leaveType);
    const defaultValue = leaveTypeConfig?.maxDaysPerYear || 0;

    const values = filteredEmployees.map(emp => 
      emp.leaveAllocations[leaveType] || defaultValue
    );

    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = Math.round(values.reduce((sum, val) => sum + val, 0) / values.length);
    
    return { min, max, avg, current: avg };
  };

  // Save all changes for an employee
  const saveAllEmployeeChanges = async () => {
    if (!selectedEmployeeForAdjustment) return Promise.resolve();
    
    try {
      setSavingAllChanges(true);
      const savePromises: Promise<any>[] = [];
      
      // Save each leave type allocation
      leaveTypes.forEach(leaveType => {
        const currentValue = selectedEmployeeForAdjustment.leaveAllocations[leaveType.leaveType];
        if (currentValue !== undefined) {
          savePromises.push(
            leaveBalancesApi.updateLeaveBalance({
              employeeId: selectedEmployeeForAdjustment.employeeId,
              leaveType: leaveType.leaveType,
              year: new Date().getFullYear(),
              totalAllocated: currentValue,
              notes: `Individual adjustment: ${currentValue} days for ${leaveType.displayName}`
            })
          );
        }
      });

      // Wait for all saves to complete
      const results = await Promise.allSettled(savePromises);
      
      // Check if all saves were successful
      const failedSaves = results.filter(result => result.status === 'rejected');
      
      if (failedSaves.length === 0) {
        onNotification({
          type: 'success',
          message: `All leave allocations saved successfully for ${selectedEmployeeForAdjustment.employeeName}`
        });
        setShowIndividualModal(false);
        setSelectedEmployeeForAdjustment(null);
      } else {
        onNotification({
          type: 'error',
          message: `Failed to save ${failedSaves.length} leave type(s). Please try again.`
        });
      }
    } catch (error: any) {
      console.error('❌ Error saving all employee changes:', error);
      onNotification({
        type: 'error',
        message: `Failed to save changes: ${error.message}`
      });
    } finally {
      setSavingAllChanges(false);
    }
  };

  // Save individual adjustment
  const saveIndividualAdjustment = async () => {
    try {
      if (!adjustmentModal.employee || !adjustmentModal.leaveType) return;

      setLoading(true);

      // Save to backend database
      const response = await leaveBalancesApi.updateLeaveBalance({
        employeeId: adjustmentModal.employee.employeeId,
        leaveType: adjustmentModal.leaveType,
        year: new Date().getFullYear(),
        totalAllocated: adjustmentValue,
        notes: adjustmentReason || `Individual adjustment: ${adjustmentValue} days`
      });

      if (response.success) {
        // Update local state after successful API call
        setEmployees(prev => prev.map(emp => 
          emp.employeeId === adjustmentModal.employee!.employeeId
            ? {
                ...emp,
                leaveAllocations: {
                  ...emp.leaveAllocations,
                  [adjustmentModal.leaveType!]: adjustmentValue
                }
              }
            : emp
        ));

        onNotification({
          type: 'success',
          message: `Leave allocation updated for ${adjustmentModal.employee.employeeName} and saved to database`
        });

        setAdjustmentModal({ isOpen: false, type: 'individual' });
        // Force reload employees from backend to get latest data
        await loadEmployees();
      } else {
        throw new Error(response.message || 'Failed to save to database');
      }
    } catch (error: any) {
      console.error('❌ Error saving individual adjustment:', error);
      onNotification({ 
        type: 'error', 
        message: `Failed to save adjustment: ${error.message}` 
      });
    } finally {
      setLoading(false);
    }
  };

  // Save bulk adjustments
  const saveBulkAdjustments = async () => {
    try {
      const filteredEmployees = getFilteredEmployeesForBulk();
      
      console.log('💾 Starting bulk adjustments save:', {
        employeeCount: filteredEmployees.length,
        adjustmentMode,
        bulkAdjustments,
        reason: adjustmentReason
      });

      if (filteredEmployees.length === 0) {
        onNotification({ type: 'error', message: 'No employees selected' });
        return;
      }

      // Check if any adjustments are made
      const hasAdjustments = Object.keys(bulkAdjustments).length > 0;
      if (!hasAdjustments) {
        onNotification({ type: 'error', message: 'No leave type adjustments specified. Please set values for at least one leave type.' });
        return;
      }

      setLoading(true);

      // Prepare bulk save data
      const allocationData: any[] = [];
      
      filteredEmployees.forEach(employee => {
        Object.entries(bulkAdjustments).forEach(([leaveType, adjustmentValue]) => {
          if (adjustmentValue !== undefined && adjustmentValue !== null) {
            const currentValue = employee.leaveAllocations[leaveType] || 0;
            let newValue = currentValue;

            // Calculate new value based on adjustment mode
            switch (adjustmentMode) {
              case 'set':
                newValue = adjustmentValue;
                break;
              case 'add':
                newValue = currentValue + adjustmentValue;
                break;
              case 'subtract':
                newValue = Math.max(0, currentValue - adjustmentValue);
                break;
            }

            console.log(`🔄 Processing ${employee.employeeName} - ${leaveType}: ${currentValue} → ${newValue} (${adjustmentMode})`);

            // Add to bulk save data
            allocationData.push({
              employeeId: employee.employeeId,
              leaveType: leaveType,
              year: new Date().getFullYear(),
              totalAllocated: newValue,
              notes: adjustmentReason || `Bulk adjustment: ${adjustmentMode} ${adjustmentValue} days for ${leaveType}`
            });
          }
        });
      });

      console.log(`📡 Making bulk save API call for ${allocationData.length} allocations...`);

      // Use bulk save API instead of individual calls
      const response = await leaveBalancesApi.saveAllAllocations(
        filteredEmployees.map(employee => ({
          employeeId: employee.employeeId,
          employeeName: employee.employeeName,
          leaveAllocations: Object.fromEntries(
            Object.entries(bulkAdjustments).map(([leaveType, adjustmentValue]) => {
              const currentValue = employee.leaveAllocations[leaveType] || 0;
              let newValue = currentValue;

              switch (adjustmentMode) {
                case 'set':
                  newValue = adjustmentValue;
                  break;
                case 'add':
                  newValue = currentValue + adjustmentValue;
                  break;
                case 'subtract':
                  newValue = Math.max(0, currentValue - adjustmentValue);
                  break;
              }

              return [leaveType, newValue];
            })
          )
        }))
      );
      
      console.log('📡 Bulk adjustment API response:', response);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to save bulk adjustments');
      }

      // Update local state after successful API calls
      setEmployees(prev => prev.map(emp => {
        // Check if this employee is in the filtered list
        const isTargetEmployee = filteredEmployees.find(fe => fe.employeeId === emp.employeeId);
        if (!isTargetEmployee) {
          return emp; // Skip employees not in the filtered list
        }
        
        const updatedAllocations = { ...emp.leaveAllocations };
        
        Object.entries(bulkAdjustments).forEach(([leaveType, adjustmentValue]) => {
          if (adjustmentValue !== undefined && adjustmentValue !== null) {
            const currentValue = updatedAllocations[leaveType] || 0;

            // Calculate new value based on adjustment mode
            switch (adjustmentMode) {
              case 'set':
                updatedAllocations[leaveType] = adjustmentValue;
                break;
              case 'add':
                updatedAllocations[leaveType] = currentValue + adjustmentValue;
                break;
              case 'subtract':
                updatedAllocations[leaveType] = Math.max(0, currentValue - adjustmentValue);
                break;
            }
          }
        });

        return {
          ...emp,
          leaveAllocations: updatedAllocations
        };
      }));

      const adjustmentCount = Object.values(bulkAdjustments).filter(val => val !== undefined && val !== null).length;
      const modeText = adjustmentMode === 'set' ? 'set' : adjustmentMode === 'add' ? 'added to' : 'subtracted from';

      console.log('✅ Bulk adjustments saved successfully');

      onNotification({
        type: 'success',
        message: `Successfully ${modeText} leave allocations for ${filteredEmployees.length} employees (${adjustmentCount} leave types affected) and saved to database`
      });

      // Reset form and close modal
      setBulkAdjustments({});
      setAdjustmentReason('');
      setAdjustmentMode('set');
      setAdjustmentModal({ isOpen: false, type: 'individual' });
      setShowEmployeeDropdown(false);
      setShowDepartmentDropdown(false);

    } catch (error: any) {
      console.error('❌ Error saving bulk adjustments:', error);
      onNotification({ 
        type: 'error', 
        message: `Failed to save bulk adjustments: ${error.message}` 
      });
    } finally {
      setLoading(false);
    }
  };



  // Handle export
  const handleExport = () => {
    const headers = ['Employee', 'Code', 'Department', 'Position'];
    leaveTypes.forEach(lt => headers.push(lt.displayName));
    headers.push('Total Leaves');

    const csvData = employees.map(emp => {
      const row = [
        emp.employeeName,
        emp.employeeCode,
        emp.department,
        emp.position
      ];
      
      let totalLeaves = 0;
      leaveTypes.forEach(lt => {
        const leaveValue = emp.leaveAllocations[lt.leaveType] || lt.maxDaysPerYear;
        row.push(leaveValue.toString());
        totalLeaves += leaveValue;
      });
      
      row.push(totalLeaves.toString());
      
      return row;
    });

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `leave-allocations-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Component cleanup effect
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Add global error handler for unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection in LeaveAllocationsTab:', event.reason);
      // Prevent the default browser behavior
      event.preventDefault();
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // Load initial data silently without showing loading state
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Load leave types first, then employees
        const leaveTypesResult = await loadLeaveTypes();
        
        // Only load employees after leave types are confirmed
        if (leaveTypesResult === true) {
          await loadEmployees();
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        // Set initial loading to false after first load completes
        setInitialLoading(false);
      }
    };

    loadInitialData().catch(error => {
      console.error('Error in loadInitialData:', error);
      setInitialLoading(false);
    });
  }, []);

  // Debounced effect for search and filter changes with reduced debounce time
  useEffect(() => {
    if (leaveTypes.length > 0) {
      const timeoutId = setTimeout(() => {
        loadEmployees().catch(error => {
          console.error('Error in debounced loadEmployees:', error);
        });
      }, 150); // Reduced from 300ms to 150ms for faster response

      return () => clearTimeout(timeoutId);
    }
  }, [leaveTypes, search, departmentFilter, loadEmployees]);

  // Component for individual leave type adjustment row
  const LeaveTypeAdjustmentRow: React.FC<{
    leaveType: LeaveType;
    currentValue: number;
    employeeId: number;
    onUpdate: (leaveType: string, newValue: number) => void;
  }> = ({ leaveType, currentValue, employeeId, onUpdate }) => {
    const [tempValue, setTempValue] = useState(currentValue);
    const [saving, setSaving] = useState(false);

    const handleIncrement = () => {
      const newValue = tempValue + 1;
      setTempValue(newValue);
    };

    const handleDecrement = () => {
      const newValue = Math.max(0, tempValue - 1);
      setTempValue(newValue);
    };

    const handleInputChange = (value: number) => {
      const newValue = Math.max(0, value);
      setTempValue(newValue);
    };

    const handleApply = async () => {
      try {
        setSaving(true);
        
        // Save to backend database
        const response = await leaveBalancesApi.updateLeaveBalance({
          employeeId: employeeId,
          leaveType: leaveType.leaveType,
          year: new Date().getFullYear(),
          totalAllocated: tempValue,
          notes: `Individual adjustment: ${tempValue} days for ${leaveType.displayName}`
        });

        if (response.success) {
          // Update local state after successful API call
          onUpdate(leaveType.leaveType, tempValue);
          onNotification({
            type: 'success',
            message: `${leaveType.displayName} updated to ${tempValue} days and saved to database`
          });
        } else {
          throw new Error(response.message || 'Failed to save to database');
        }
      } catch (error: any) {
        console.error('❌ Error saving leave type adjustment:', error);
        onNotification({
          type: 'error',
          message: `Failed to save ${leaveType.displayName}: ${error.message}`
        });
        // Reset to original value on error
        setTempValue(currentValue);
      } finally {
        setSaving(false);
      }
    };

    const handleReset = () => {
      setTempValue(currentValue);
    };

    const hasChanged = tempValue !== currentValue;

    return (
      <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h5 className="font-medium text-gray-900">{leaveType.displayName}</h5>
            <p className="text-xs text-gray-500">Max: {leaveType.maxDaysPerYear} days</p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleDecrement}
              className="w-8 h-8 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center transition-colors"
            >
              <Minus className="h-4 w-4" />
            </button>
            <input
              type="number"
              min="0"
              value={tempValue}
              onChange={(e) => handleInputChange(Number(e.target.value))}
              className="w-16 text-center text-lg font-bold border-2 border-gray-300 rounded-lg py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="0"
            />
            <button
              onClick={handleIncrement}
              className="w-8 h-8 rounded-full bg-green-100 text-green-600 hover:bg-green-200 flex items-center justify-center transition-colors"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {hasChanged && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">
              Change: {tempValue > currentValue ? '+' : ''}{tempValue - currentValue}
            </span>
            <div className="flex space-x-2">
              <button
                onClick={handleReset}
                className="text-xs px-2 py-1 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Reset
              </button>
              <button
                onClick={handleApply}
                disabled={saving}
                className="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : 'Apply'}
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Leave Allocations</h3>
          <p className="text-sm text-gray-600">
            Dynamic leave types and allocations based on policy configuration
          </p>
          
          {/* Loading Progress Indicator - only show during subsequent operations */}
          {loading && !initialLoading && (
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-600 rounded-full animate-pulse"></div>
                <span className="text-sm text-blue-600">Loading employee data...</span>
              </div>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleBulkAdjustment}
            className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700"
          >
            <Users className="h-4 w-4 mr-2" />
            Bulk Adjust
          </button>
          <button
            onClick={handleExport}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* No Leave Types Warning */}
      {leaveTypes.length === 0 && !initialLoading && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <Settings className="h-5 w-5 text-yellow-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">No Leave Types Configured</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Please configure leave types in the Leave Policy Configuration tab first.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search employees..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        
        <div className="w-full sm:w-48">
          <select
            value={departmentFilter}
            onChange={(e) => setDepartmentFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Departments</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </select>
        </div>

        <div className="w-full sm:w-32">
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={10}>10 per page</option>
            <option value={25}>25 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>
        </div>
      </div>

      {/* Table */}
      {(leaveTypes.length > 0 || initialLoading) && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Department
                  </th>
                  {leaveTypes.length > 0 ? leaveTypes.map((leaveType, index) => (
                    <th key={`header-${leaveType.leaveType}-${index}`} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                      <div className="text-center">
                        <div className="font-semibold">{leaveType.displayName}</div>
                        <div className="text-xs text-gray-400 mt-1">({leaveType.maxDaysPerYear} days)</div>
                      </div>
                    </th>
                  )) : (
                    // Show placeholder headers during initial loading
                    <>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                        <div className="text-center">
                          <div className="font-semibold">Annual Leave</div>
                          <div className="text-xs text-gray-400 mt-1">(-- days)</div>
                        </div>
                      </th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                        <div className="text-center">
                          <div className="font-semibold">Sick Leave</div>
                          <div className="text-xs text-gray-400 mt-1">(-- days)</div>
                        </div>
                      </th>
                      <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                        <div className="text-center">
                          <div className="font-semibold">Casual Leave</div>
                          <div className="text-xs text-gray-400 mt-1">(-- days)</div>
                        </div>
                      </th>
                    </>
                  )}
                  <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                    Total Leaves
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {initialLoading ? (
                  // Show minimal loading only on initial load
                  <tr>
                    <td colSpan={4 + (leaveTypes.length || 3)} className="px-6 py-8 text-center text-gray-500">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="w-4 h-4 bg-blue-600 rounded-full animate-bounce"></div>
                        <span>Loading leave allocations...</span>
                      </div>
                    </td>
                  </tr>
                ) : loading && employees.length === 0 ? (
                  // Loading skeleton rows for better UX only when no data exists
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={`skeleton-${index}`} className="animate-pulse">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-32"></div>
                          <div className="h-3 bg-gray-200 rounded w-20"></div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-28"></div>
                          <div className="h-3 bg-gray-200 rounded w-24"></div>
                        </div>
                      </td>
                      {Array.from({ length: leaveTypes.length }).map((_, ltIndex) => (
                        <td key={`skeleton-lt-${ltIndex}`} className="px-3 py-4 whitespace-nowrap text-center">
                          <div className="h-6 bg-gray-200 rounded w-8 mx-auto"></div>
                        </td>
                      ))}
                      <td className="px-3 py-4 whitespace-nowrap text-center">
                        <div className="h-6 bg-gray-200 rounded w-10 mx-auto"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="h-6 bg-gray-200 rounded w-6 mx-auto"></div>
                      </td>
                    </tr>
                  ))
                ) : currentEmployees.length === 0 ? (
                  <tr>
                    <td colSpan={4 + leaveTypes.length} className="px-6 py-4 text-center text-gray-500">
                      No employees found
                    </td>
                  </tr>
                ) : (
                  currentEmployees.map((employee) => {
                    // Calculate total leaves for this employee
                    const totalLeaves = leaveTypes.reduce((total, leaveType) => {
                      return total + (employee.leaveAllocations[leaveType.leaveType] || leaveType.maxDaysPerYear);
                    }, 0);

                    return (
                      <tr key={employee.employeeId} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {employee.employeeName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {employee.employeeCode}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{employee.department}</div>
                          <div className="text-sm text-gray-500">{employee.position}</div>
                        </td>
                        {leaveTypes.map((leaveType, index) => {
                          const currentValue = employee.leaveAllocations[leaveType.leaveType] || leaveType.maxDaysPerYear;
                          return (
                            <td key={`cell-${employee.employeeId}-${leaveType.leaveType}-${index}`} className="px-3 py-4 whitespace-nowrap text-center">
                              <span className="text-sm font-medium text-gray-900">
                                {Math.round(currentValue)}
                              </span>
                            </td>
                          );
                        })}
                        <td className="px-3 py-4 whitespace-nowrap text-center">
                          <span className="text-sm font-bold text-gray-900">
                            {Math.round(totalLeaves)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <button
                            onClick={() => handleEmployeeActions(employee)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Adjust leave allocations"
                          >
                            <Edit3 className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {employees.length > 0 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                    <span className="font-medium">{Math.min(endIndex, employees.length)}</span> of{' '}
                    <span className="font-medium">{totalEmployees > 0 ? totalEmployees : employees.length}</span> results
                    {hasMoreData && (
                      <span className="text-blue-600 ml-2">(More available)</span>
                    )}
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    
                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber;
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }
                      
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => setCurrentPage(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNumber
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
          
          {/* Load More Button */}
          {hasMoreData && !loading && !initialLoading && (
            <div className="text-center py-4">
              <button
                onClick={loadMoreEmployees}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Loading...' : 'Load More Employees'}
              </button>
              <p className="text-xs text-gray-500 mt-2">
                Showing {employees.length} of {totalEmployees} employees
              </p>
            </div>
          )}
        </div>
      )}

      {/* Ultra Professional Adjustment Modal */}
      {adjustmentModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-gray-100">
            {/* Professional Header */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-8 py-6 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                    {adjustmentModal.type === 'individual' ? 
                      <User className="h-6 w-6 text-white" /> : 
                      <Users className="h-6 w-6 text-white" />
                    }
                  </div>
              <div>
                    <h2 className="text-2xl font-bold">
                      {adjustmentModal.type === 'individual' ? 'Individual Adjustment' : 'Bulk Leave Adjustment'}
                    </h2>
                    <p className="text-blue-100 mt-1 text-sm">
                  {adjustmentModal.type === 'individual' 
                    ? 'Modify leave allocation for a single employee'
                    : 'Apply leave adjustments to multiple employees at once'
                  }
                </p>
                  </div>
              </div>
              <button
                onClick={() => {
                  setAdjustmentModal({ isOpen: false, type: 'individual' });
                  setShowEmployeeDropdown(false);
                  setShowDepartmentDropdown(false);
                }}
                  className="w-10 h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-xl flex items-center justify-center transition-all"
              >
                  <X className="h-5 w-5 text-white" />
              </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-8 max-h-[calc(90vh-120px)] overflow-y-auto">

            {adjustmentModal.type === 'individual' ? (
              <div className="space-y-6">
                {/* Employee Info Card */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 h-12 w-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                      <span className="text-lg font-medium text-white">
                        {adjustmentModal.employee?.employeeName?.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-medium text-gray-900">
                        {adjustmentModal.employee?.employeeName}
                      </h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="font-medium">{adjustmentModal.employee?.employeeCode}</span>
                        <span>•</span>
                        <span>{adjustmentModal.employee?.department}</span>
                        <span>•</span>
                        <span>{leaveTypes.find(lt => lt.leaveType === adjustmentModal.leaveType)?.displayName}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">{adjustmentModal.currentValue}</div>
                      <div className="text-xs text-gray-600">Current Days</div>
                    </div>
                  </div>
                </div>

                {/* Adjustment Controls */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">New Allocation</h4>
                  <div className="flex items-center justify-center space-x-4">
                    <button
                      onClick={() => setAdjustmentValue(Math.max(0, adjustmentValue - 1))}
                      className="w-12 h-12 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center transition-colors"
                    >
                      <Minus className="h-6 w-6" />
                    </button>
                    <input
                      type="number"
                      min="0"
                      value={adjustmentValue}
                      onChange={(e) => setAdjustmentValue(Number(e.target.value))}
                      className="w-24 text-center text-2xl font-bold border-2 border-gray-300 rounded-lg py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      onClick={() => setAdjustmentValue(adjustmentValue + 1)}
                      className="w-12 h-12 rounded-full bg-green-100 text-green-600 hover:bg-green-200 flex items-center justify-center transition-colors"
                    >
                      <Plus className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="text-center mt-2">
                    <span className="text-sm text-gray-600">
                      Change: {adjustmentValue > (adjustmentModal.currentValue || 0) ? '+' : ''}{adjustmentValue - (adjustmentModal.currentValue || 0)} days
                    </span>
                  </div>
                </div>

                {/* Reason */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for Adjustment
                  </label>
                  <textarea
                    value={adjustmentReason}
                    onChange={(e) => setAdjustmentReason(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter reason for adjustment..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setAdjustmentModal({ isOpen: false, type: 'individual' })}
                    className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={saveIndividualAdjustment}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    <Save className="h-4 w-4 inline mr-2" />
                    Save Adjustment
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Professional Employee Selection */}
                <div className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                <div>
                        <h4 className="text-lg font-bold text-gray-900">Target Selection</h4>
                        <p className="text-sm text-gray-600">Choose employees for bulk adjustment</p>
                      </div>
                    </div>
                    {/* Selection Summary */}
                    <div className="bg-white rounded-lg px-4 py-2 border border-gray-200 shadow-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-semibold text-gray-900">
                          {getFilteredEmployeesForBulk().length} Selected
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Selection Methods Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Select All Option */}
                    <div className="bg-white rounded-xl p-4 border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all group">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                            <Users className="h-4 w-4 text-blue-600" />
                          </div>
                        <div>
                            <h5 className="text-sm font-semibold text-gray-900">All Employees</h5>
                            <p className="text-xs text-gray-500">{employees.length} total</p>
                          </div>
                        </div>
                        </div>
                        <button
                          onClick={toggleSelectAllEmployees}
                        className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-all ${
                            bulkFilters.selectAll 
                            ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm'
                            : 'bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-700'
                          }`}
                        >
                        {bulkFilters.selectAll ? '✓ All Selected' : 'Select All'}
                        </button>
                    </div>

                    {/* Department Filter */}
                    <div className="bg-white rounded-xl p-4 border border-gray-200 hover:border-orange-300 hover:shadow-md transition-all group relative">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-orange-50 rounded-lg flex items-center justify-center group-hover:bg-orange-100 transition-colors">
                            <Filter className="h-4 w-4 text-orange-600" />
                          </div>
                          <div>
                            <h5 className="text-sm font-semibold text-gray-900">By Department</h5>
                            <p className="text-xs text-gray-500">{departments.length} departments</p>
                            </div>
                          </div>
                      </div>
                      <button
                        onClick={() => setShowDepartmentDropdown(!showDepartmentDropdown)}
                        className="w-full py-2 px-3 rounded-lg text-sm font-medium bg-gray-100 text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all flex items-center justify-between"
                      >
                        <span>Filter Departments</span>
                        <div className="flex items-center space-x-2">
                          <span className="bg-orange-100 text-orange-700 px-2 py-0.5 rounded-full text-xs font-medium">
                            {bulkFilters.selectedDepartments.length || departments.length}
                          </span>
                          <ChevronDown className={`h-4 w-4 transition-transform ${showDepartmentDropdown ? 'rotate-180' : ''}`} />
                        </div>
                      </button>
                      
                      {showDepartmentDropdown && (
                        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-30 overflow-hidden">
                          <div className="p-4 border-b border-gray-100 bg-gray-50">
                            <h6 className="text-xs font-bold text-gray-700 uppercase tracking-wider">Select Departments</h6>
                          </div>
                          <div className="max-h-48 overflow-y-auto">
                          {departments.map(dept => (
                              <label key={dept} className="flex items-center px-4 py-3 hover:bg-orange-25 hover:bg-orange-50 cursor-pointer group/dept transition-colors">
                              <input
                                type="checkbox"
                                checked={bulkFilters.selectedDepartments.includes(dept)}
                                onChange={() => toggleDepartmentSelection(dept)}
                                  className="w-4 h-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500 focus:ring-2"
                              />
                                <div className="ml-3 flex-1">
                                  <span className="text-sm font-medium text-gray-900 group-hover/dept:text-orange-900">{dept}</span>
                                <div className="text-xs text-gray-500">
                                  {employees.filter(emp => emp.department === dept).length} employees
                                </div>
                              </div>
                            </label>
                          ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Individual Selection */}
                    <div className="bg-white rounded-xl p-4 border border-gray-200 hover:border-green-300 hover:shadow-md transition-all group relative">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-50 rounded-lg flex items-center justify-center group-hover:bg-green-100 transition-colors">
                            <User className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <h5 className="text-sm font-semibold text-gray-900">Individual</h5>
                            <p className="text-xs text-gray-500">Choose specific employees</p>
                            </div>
                          </div>
                      </div>
                      <button
                        onClick={() => setShowEmployeeDropdown(!showEmployeeDropdown)}
                        className="w-full py-2 px-3 rounded-lg text-sm font-medium bg-gray-100 text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all flex items-center justify-between"
                      >
                        <span>Select Employees</span>
                        <div className="flex items-center space-x-2">
                          <span className="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs font-medium">
                            {bulkFilters.selectedEmployees.length}
                          </span>
                          <ChevronDown className={`h-4 w-4 transition-transform ${showEmployeeDropdown ? 'rotate-180' : ''}`} />
                        </div>
                      </button>
                      
                      {showEmployeeDropdown && (
                        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-30 overflow-hidden">
                          <div className="p-4 border-b border-gray-100">
                            <div className="relative">
                              <Search className="h-4 w-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                              <input
                                type="text"
                                placeholder="Search employees..."
                                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50 focus:bg-white transition-colors"
                              />
                            </div>
                          </div>
                          <div className="max-h-64 overflow-y-auto">
                          {employees.map(emp => (
                              <label key={emp.employeeId} className="flex items-center px-4 py-3 hover:bg-green-50 cursor-pointer group/emp transition-colors">
                              <input
                                type="checkbox"
                                checked={bulkFilters.selectedEmployees.includes(emp.employeeId)}
                                onChange={() => toggleEmployeeSelection(emp.employeeId)}
                                  className="w-4 h-4 text-green-600 rounded border-gray-300 focus:ring-green-500 focus:ring-2"
                              />
                                <div className="ml-3 flex-1">
                                  <span className="text-sm font-medium text-gray-900 group-hover/emp:text-green-900">{emp.employeeName}</span>
                                <div className="text-xs text-gray-500">{emp.employeeCode} • {emp.department}</div>
                              </div>
                            </label>
                          ))}
                          </div>
                        </div>
                      )}
                    </div>
                    </div>
                  </div>

                                {/* Leave Adjustment Section */}
                {getFilteredEmployeesForBulk().length > 0 && (
                  <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <Edit3 className="h-5 w-5 text-green-600" />
                      </div>
                        <div>
                        <h4 className="text-lg font-bold text-gray-900">Leave Adjustments</h4>
                        <p className="text-sm text-gray-600">Set new leave allocations for selected employees</p>
                          </div>
                        </div>

                    {/* Adjustment Mode Selection */}
                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Adjustment Mode</label>
                      <div className="grid grid-cols-3 gap-3">
                        <label className="relative cursor-pointer">
                          <input
                            type="radio"
                            name="adjustmentMode"
                            value="set"
                            checked={adjustmentMode === 'set'}
                            onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                            className="sr-only"
                          />
                          <div className={`border-2 rounded-lg p-3 transition-all ${
                            adjustmentMode === 'set'
                              ? 'bg-blue-50 border-blue-200 shadow-sm'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                          }`}>
                            <div className="text-center">
                              <div className={`font-semibold text-sm ${
                                adjustmentMode === 'set' ? 'text-blue-600' : 'text-gray-600'
                              }`}>
                                Set Value
                      </div>
                              <div className={`text-xs ${
                                adjustmentMode === 'set' ? 'text-blue-500' : 'text-gray-500'
                              }`}>
                                Replace current allocation
                      </div>
                    </div>
                          </div>
                        </label>
                        <label className="relative cursor-pointer">
                          <input
                            type="radio"
                            name="adjustmentMode"
                            value="add"
                            checked={adjustmentMode === 'add'}
                            onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                            className="sr-only"
                          />
                          <div className={`border-2 rounded-lg p-3 transition-all ${
                            adjustmentMode === 'add'
                              ? 'bg-green-50 border-green-200 shadow-sm'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                          }`}>
                            <div className="text-center">
                              <div className={`font-semibold text-sm ${
                                adjustmentMode === 'add' ? 'text-green-600' : 'text-gray-600'
                              }`}>
                                Add Days
                              </div>
                              <div className={`text-xs ${
                                adjustmentMode === 'add' ? 'text-green-500' : 'text-gray-500'
                              }`}>
                                Add to current allocation
                              </div>
                            </div>
                          </div>
                        </label>
                        <label className="relative cursor-pointer">
                          <input
                            type="radio"
                            name="adjustmentMode"
                            value="subtract"
                            checked={adjustmentMode === 'subtract'}
                            onChange={(e) => setAdjustmentMode(e.target.value as 'set' | 'add' | 'subtract')}
                            className="sr-only"
                          />
                          <div className={`border-2 rounded-lg p-3 transition-all ${
                            adjustmentMode === 'subtract'
                              ? 'bg-red-50 border-red-200 shadow-sm'
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100 hover:border-gray-300'
                          }`}>
                            <div className="text-center">
                              <div className={`font-semibold text-sm ${
                                adjustmentMode === 'subtract' ? 'text-red-600' : 'text-gray-600'
                              }`}>
                                Subtract Days
                              </div>
                              <div className={`text-xs ${
                                adjustmentMode === 'subtract' ? 'text-red-500' : 'text-gray-500'
                              }`}>
                                Remove from current
                              </div>
                            </div>
                          </div>
                        </label>
                  </div>
                </div>

                {/* Leave Type Adjustments */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <label className="block text-sm font-semibold text-gray-900">Leave Type Adjustments</label>
                        <button
                          onClick={() => {
                            const currentValues: { [key: string]: number } = {};
                            leaveTypes.forEach(leaveType => {
                              const stats = getCurrentLeaveStats(leaveType.leaveType);
                              currentValues[leaveType.leaveType] = stats.avg;
                            });
                            setBulkAdjustments(currentValues);
                          }}
                          className="text-xs px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                          Reset to Current Values
                        </button>
                      </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {leaveTypes.map((leaveType) => {
                      const stats = getCurrentLeaveStats(leaveType.leaveType);
                      return (
                          <div key={leaveType.leaveType} className="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                                <h5 className="font-semibold text-gray-900 text-sm">{leaveType.displayName}</h5>
                                <div className="flex items-center space-x-2 text-xs text-gray-500">
                                  <span>Current:</span>
                                  {stats.min === stats.max ? (
                                    <span className="font-medium text-blue-600">{Math.round(stats.current)} days</span>
                                  ) : (
                                    <span className="font-medium text-blue-600">
                                      {Math.round(stats.min)}-{Math.round(stats.max)} days (avg: {Math.round(stats.avg)})
                                    </span>
                                  )}
                                </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setBulkAdjustments(prev => ({
                                ...prev,
                                    [leaveType.leaveType]: Math.max(0, (prev[leaveType.leaveType] || 0) - 1)
                              }))}
                                    className="w-8 h-8 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center transition-colors"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <input
                              type="number"
                                  min="0"
                                  max="365"
                              value={bulkAdjustments[leaveType.leaveType] || 0}
                              onChange={(e) => setBulkAdjustments(prev => ({
                                ...prev,
                                    [leaveType.leaveType]: parseInt(e.target.value) || 0
                              }))}
                                    className="w-16 text-center text-lg font-bold border-2 border-gray-300 rounded-lg py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="0"
                            />
                            <button
                              onClick={() => setBulkAdjustments(prev => ({
                                ...prev,
                                [leaveType.leaveType]: (prev[leaveType.leaveType] || 0) + 1
                              }))}
                                    className="w-8 h-8 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 flex items-center justify-center transition-colors"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        

                          </div>
                      );
                    })}
                  </div>
                </div>

                    {/* Adjustment Reason */}
                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Reason for Adjustment</label>
                  <textarea
                    value={adjustmentReason}
                    onChange={(e) => setAdjustmentReason(e.target.value)}
                    rows={3}
                        className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-gray-50 focus:bg-white transition-colors"
                        placeholder="Enter the reason for this bulk adjustment (required)..."
                  />
                </div>

                {/* Action Buttons */}
                    <div className="flex justify-between items-center pt-6 border-t border-gray-200">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>Changes will apply to {getFilteredEmployeesForBulk().length} employees</span>
                      </div>
                      <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setAdjustmentModal({ isOpen: false, type: 'individual' });
                      setShowEmployeeDropdown(false);
                      setShowDepartmentDropdown(false);
                    }}
                          className="px-6 py-2.5 text-gray-700 bg-white border-2 border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={saveBulkAdjustments}
                          disabled={Object.keys(bulkAdjustments).length === 0 || !adjustmentReason.trim()}
                          className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium shadow-lg hover:shadow-xl"
                  >
                    <Save className="h-4 w-4 inline mr-2" />
                    Apply to {getFilteredEmployeesForBulk().length} Employees
                  </button>
                      </div>
                </div>
              </div>
            )}

                {/* Empty State */}
                {getFilteredEmployeesForBulk().length === 0 && (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Select Employees First</h3>
                    <p className="text-gray-600 max-w-md mx-auto">
                      Choose employees using the selection methods above to start making bulk leave adjustments.
                    </p>
                  </div>
                )}
                          </div>
                        )}
                      </div>
          </div>
        </div>
      )}

      {/* Individual Employee Adjustment Modal */}
      {showIndividualModal && selectedEmployeeForAdjustment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  Adjust Leave Allocations
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Modify leave quotas for this employee
                </p>
              </div>
              <button
                onClick={() => {
                  setShowIndividualModal(false);
                  setSelectedEmployeeForAdjustment(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Employee Info Card */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 h-12 w-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                    <span className="text-lg font-medium text-white">
                      {selectedEmployeeForAdjustment.employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-medium text-gray-900">
                      {selectedEmployeeForAdjustment.employeeName}
                    </h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="font-medium">{selectedEmployeeForAdjustment.employeeCode}</span>
                      <span>•</span>
                      <span>{selectedEmployeeForAdjustment.department}</span>
                      <span>•</span>
                      <span>{selectedEmployeeForAdjustment.position}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Leave Types Adjustment Grid */}
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Leave Type Allocations</h4>
                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                   {leaveTypes.map((leaveType) => {
                     const currentValue = selectedEmployeeForAdjustment.leaveAllocations[leaveType.leaveType] || leaveType.maxDaysPerYear;
                    
                    return (
                      <LeaveTypeAdjustmentRow
                        key={leaveType.leaveType}
                        leaveType={leaveType}
                        currentValue={currentValue}
                        employeeId={selectedEmployeeForAdjustment.employeeId}
                        onUpdate={(leaveType, newValue) => {
                          setEmployees(prev => prev.map(emp => 
                            emp.employeeId === selectedEmployeeForAdjustment.employeeId
                              ? {
                                  ...emp,
                                  leaveAllocations: {
                                    ...emp.leaveAllocations,
                                    [leaveType]: newValue
                                  }
                                }
                              : emp
                          ));
                          setSelectedEmployeeForAdjustment(prev => prev ? {
                            ...prev,
                            leaveAllocations: {
                              ...prev.leaveAllocations,
                              [leaveType]: newValue
                            }
                          } : null);
                        }}
                      />
                    );
                  })}
                </div>
              </div>

              {/* Summary Section */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">Current Summary</h5>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  {leaveTypes.map((leaveType) => {
                    const currentValue = selectedEmployeeForAdjustment.leaveAllocations[leaveType.leaveType] || leaveType.maxDaysPerYear;
                    return (
                      <div key={leaveType.leaveType} className="bg-white rounded-lg p-3 border border-gray-200">
                        <div className="text-2xl font-bold text-blue-600">{Math.round(currentValue)}</div>
                        <div className="text-xs text-gray-600">{leaveType.displayName}</div>
                      </div>
                    );
                  })}
                  <div className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(Object.values(selectedEmployeeForAdjustment.leaveAllocations).reduce((sum, val) => sum + val, 0))}
                    </div>
                    <div className="text-xs text-gray-600">Total Days</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowIndividualModal(false);
                    setSelectedEmployeeForAdjustment(null);
                  }}
                  className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Close
                </button>
                <button
                  onClick={saveAllEmployeeChanges}
                  disabled={savingAllChanges}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {savingAllChanges ? 'Saving...' : 'Save All Changes'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaveAllocationsTab; 