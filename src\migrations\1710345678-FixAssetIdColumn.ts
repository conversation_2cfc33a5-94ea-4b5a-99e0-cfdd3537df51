import { MigrationInterface, QueryRunner } from "typeorm";

export class FixAssetIdColumn1710345678 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the assets table exists
        const tableExists = await queryRunner.hasTable('assets');
        if (!tableExists) {
            console.log('Assets table does not exist, skipping migration');
            return;
        }

        // Check if the id column is already a primary key
        const table = await queryRunner.getTable('assets');
        const idColumn = table?.findColumnByName('id');
        
        if (!idColumn) {
            console.log('ID column not found, creating it');
            await queryRunner.query(`ALTER TABLE assets ADD COLUMN id VARCHAR(36)`);
            
            // Generate UUIDs for any existing records without IDs
            await queryRunner.query(`
                UPDATE assets 
                SET id = uuid_generate_v4() 
                WHERE id IS NULL OR id = ''
            `);
            
            // Make the id column NOT NULL
            await queryRunner.query(`ALTER TABLE assets ALTER COLUMN id SET NOT NULL`);
            
            // Add primary key constraint
            await queryRunner.query(`ALTER TABLE assets ADD PRIMARY KEY (id)`);
        } else if (!idColumn.isPrimary) {
            console.log('ID column exists but is not a primary key, fixing');
            
            // Generate UUIDs for any existing records without IDs
            await queryRunner.query(`
                UPDATE assets 
                SET id = uuid_generate_v4() 
                WHERE id IS NULL OR id = ''
            `);
            
            // Make the id column NOT NULL
            await queryRunner.query(`ALTER TABLE assets ALTER COLUMN id SET NOT NULL`);
            
            // Add primary key constraint
            await queryRunner.query(`ALTER TABLE assets ADD PRIMARY KEY (id)`);
        } else {
            console.log('ID column is already a primary key, no changes needed');
        }
        
        // Ensure assetTag column exists
        const assetTagColumn = table?.findColumnByName('assetTag');
        if (!assetTagColumn) {
            console.log('assetTag column not found, creating it');
            await queryRunner.query(`ALTER TABLE assets ADD COLUMN "assetTag" VARCHAR(50)`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This migration should not be reversed as it fixes data integrity
        console.log('This migration cannot be reversed');
    }
} 