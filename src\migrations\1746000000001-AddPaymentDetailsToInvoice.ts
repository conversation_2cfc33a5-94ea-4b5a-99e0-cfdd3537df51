import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPaymentDetailsToInvoice1741000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the billing_invoices table exists
        const tableExists = await queryRunner.hasTable('billing_invoices');
        if (!tableExists) {
            console.log('billing_invoices table does not exist, skipping migration');
            return;
        }

        // Get the existing columns in the billing_invoices table
        const columns = await queryRunner.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'billing_invoices'
        `);
        
        const columnNames = columns.map((column: { COLUMN_NAME: string }) => column.COLUMN_NAME);
        
        // Add columns that don't exist
        if (!columnNames.includes('accountTitle')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN accountTitle VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('mobileNumber')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN mobileNumber VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('checkNumber')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN checkNumber VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('bankName')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN bankName VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('receivedBy')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN receivedBy VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('receiptNumber')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN receiptNumber VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('paymentNotes')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN paymentNotes TEXT NULL`);
        }

        console.log('Successfully added payment detail fields to billing_invoices table');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if the billing_invoices table exists
        const tableExists = await queryRunner.hasTable('billing_invoices');
        if (!tableExists) {
            console.log('billing_invoices table does not exist, skipping reversion');
            return;
        }

        // Remove the payment detail columns
        await queryRunner.query(`
            ALTER TABLE billing_invoices
            DROP COLUMN IF EXISTS accountTitle,
            DROP COLUMN IF EXISTS mobileNumber,
            DROP COLUMN IF EXISTS checkNumber,
            DROP COLUMN IF EXISTS bankName,
            DROP COLUMN IF EXISTS receivedBy,
            DROP COLUMN IF EXISTS receiptNumber,
            DROP COLUMN IF EXISTS paymentNotes
        `);

        console.log('Successfully removed payment detail fields from billing_invoices table');
    }
} 