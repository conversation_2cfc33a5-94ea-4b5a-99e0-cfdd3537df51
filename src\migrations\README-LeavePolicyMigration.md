# Leave Policy Settings Migration

## Overview
This migration simplifies the leave policy settings by removing unnecessary columns and keeping only the essential fields that match our streamlined UI interface.

## Migration Details

### File: `1750300000000-SimplifyLeavePolicySettings.ts`

**Purpose**: Remove unnecessary columns from the `leave_policy_settings` table to match the simplified UI interface.

### Columns Removed
The following columns have been removed from the `leave_policy_settings` table:

- `maxConsecutiveDays` - Maximum consecutive days limit
- `allowHalfDay` - Half-day leave permission
- `requiresApproval` - Approval requirement flag
- `allowNegativeBalance` - Negative balance permission
- `restrictLeavesTaken` - Month restrictions (JSON array)
- `autoApprovalLimit` - Auto-approval day limit
- `weekendsBetweenLeavesCount` - Weekend counting flag
- `holidaysBetweenLeavesCount` - Holiday counting flag
- `allowCompensatoryTimeOff` - Compensatory leave permission
- `compensatoryExpiryDays` - Compensatory leave expiry
- `approvalWorkflow` - Workflow type enum
- `workingHoursPerDay` - Working hours configuration
- `maxDaysPerRequest` - Maximum days per request
- `allowBackdatedLeave` - Backdated leave permission
- `requireDocuments` - Document requirement flag
- `autoCalculateWorkingDays` - Auto-calculation flag

### Columns Retained
The following essential columns are kept:

- `id` - Primary key
- `minDaysNotice` - Minimum advance notice required
- `allowLeaveModification` - Allow modification of pending requests
- `isActive` - Active status flag
- `createdBy` - Creator user ID
- `updatedBy` - Last updater user ID
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

## Running the Migration

### Option 1: Using the Migration Script
```bash
# Navigate to the project root
cd /path/to/your/project

# Run the migration script
npx ts-node src/scripts/runLeavePolicyMigration.ts
```

### Option 2: Using TypeORM CLI
```bash
# Generate migration (if needed)
npx typeorm migration:generate -d src/config/database.ts src/migrations/SimplifyLeavePolicySettings

# Run migration
npx typeorm migration:run -d src/config/database.ts
```

### Option 3: Manual SQL (if needed)
```sql
-- Remove unnecessary columns from leave_policy_settings table
ALTER TABLE leave_policy_settings 
DROP COLUMN IF EXISTS maxConsecutiveDays,
DROP COLUMN IF EXISTS allowHalfDay,
DROP COLUMN IF EXISTS requiresApproval,
DROP COLUMN IF EXISTS allowNegativeBalance,
DROP COLUMN IF EXISTS restrictLeavesTaken,
DROP COLUMN IF EXISTS autoApprovalLimit,
DROP COLUMN IF EXISTS weekendsBetweenLeavesCount,
DROP COLUMN IF EXISTS holidaysBetweenLeavesCount,
DROP COLUMN IF EXISTS allowCompensatoryTimeOff,
DROP COLUMN IF EXISTS compensatoryExpiryDays,
DROP COLUMN IF EXISTS approvalWorkflow,
DROP COLUMN IF EXISTS workingHoursPerDay,
DROP COLUMN IF EXISTS maxDaysPerRequest,
DROP COLUMN IF EXISTS allowBackdatedLeave,
DROP COLUMN IF EXISTS requireDocuments,
DROP COLUMN IF EXISTS autoCalculateWorkingDays;
```

## Rollback Instructions

If you need to rollback this migration:

```bash
# Using TypeORM CLI
npx typeorm migration:revert -d src/config/database.ts
```

Or run the migration's `down()` method manually:
```typescript
const migration = new SimplifyLeavePolicySettings1750300000000();
await migration.down(queryRunner);
```

## Impact Assessment

### ✅ What's Safe
- **UI Interface**: Already updated to match simplified schema
- **API Responses**: Mock data provides fallback for missing backend
- **Frontend Code**: TypeScript interfaces updated to match new schema

### ⚠️ What Needs Attention
- **Existing Data**: Any existing leave policy records will lose the removed column data
- **Backend Controllers**: May need updates if they reference removed fields
- **Reports/Analytics**: Any reports using removed fields will need updates

### 🔄 Related Files Updated
- `src/types/attendance.ts` - LeavePolicySettings interface
- `src/entities/LeavePolicySettings.ts` - Entity definition
- `src/controllers/leavePolicyController.ts` - Default settings method
- `src/services/leavePolicyApi.ts` - Mock data structure
- `src/components/HR/leave/LeavePolicyConfiguration.tsx` - UI components

## Testing Checklist

After running the migration:

- [ ] Verify database schema matches expected structure
- [ ] Test leave policy configuration UI loads without errors
- [ ] Confirm save/update operations work correctly
- [ ] Check that existing leave policy data is preserved (for retained columns)
- [ ] Validate API endpoints return expected simplified data structure

## Notes

- This migration is **irreversible** in terms of data - removed column data will be lost
- The migration includes safety checks to handle missing tables/columns gracefully
- Mock data in the API service ensures frontend functionality during backend unavailability
- Consider backing up the database before running this migration in production

## Support

If you encounter issues with this migration:

1. Check the console output for specific error messages
2. Verify database connection and permissions
3. Ensure all related TypeScript interfaces are updated
4. Review the migration logs for any skipped operations 