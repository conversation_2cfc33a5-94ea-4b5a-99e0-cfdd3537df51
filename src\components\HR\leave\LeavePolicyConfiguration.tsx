import React, { useState, useEffect } from 'react';
import { 
  Calendar,
  Settings,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Info,
  Users,
  Building2,
  CalendarDays,
  Workflow
} from 'lucide-react';
import { leavePolicyApi, leaveTypesApi, LeavePolicyConfiguration as LeavePolicyConfigurationType, LeaveTypePolicy } from '../../../services/leavePolicyApi';

interface LeavePolicyConfigurationProps {
  onSave?: (policies: LeavePolicyData) => void;
  onCancel?: () => void;
  initialData?: LeavePolicyData;
}

interface LeavePolicyData {
  leaveTypes: LeaveTypePolicy[];
}

const getDefaultLeaveTypes = (): LeaveTypePolicy[] => [];

const LeavePolicyConfiguration: React.FC<LeavePolicyConfigurationProps> = ({
  onSave,
  onCancel,
  initialData
}) => {
  const [activeTab, setActiveTab] = useState<'leaveTypes' | 'versions'>('leaveTypes');
  const [leaveTypes, setLeaveTypes] = useState<LeaveTypePolicy[]>([]);
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveTypePolicy | null>(null);
  const [showLeaveTypeModal, setShowLeaveTypeModal] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [notification, setNotification] = useState<{ type: 'success' | 'error' | 'info'; message: string } | null>(null);

  // Load initial data if provided
  useEffect(() => {
    if (initialData) {
      setLeaveTypes(initialData.leaveTypes || []);
    } else {
      // Load existing policies from API or use defaults if none exist
      loadLeavePolicies();
    }
  }, [initialData]);

  const loadLeavePolicies = async () => {
    try {
      // Load current policy configuration
      const policyResponse = await leavePolicyApi.getCurrent();
      if (policyResponse.success) {
        const policy = policyResponse.data;
        setLeaveTypes(policy.leaveTypes || []);
        // console.log('✅ Policy configuration loaded from API:', policy);
      } else {
        console.warn('⚠️ Failed to load policy configuration:', policyResponse.error);
        // Fallback to empty array
        setLeaveTypes([]);
      }
    } catch (error) {
      console.error('❌ Failed to load leave policies:', error);
      // Fallback to defaults
      setLeaveTypes([]);
      showNotification('error', 'Failed to connect to server. Please check your connection.');
    }
  };

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleSave = async () => {
    try {
      // Prepare complete policy configuration
      const policyConfiguration: Omit<LeavePolicyConfigurationType, 'id' | 'createdAt' | 'updatedAt'> = {
        leaveTypes,
        holidayCalendars: [], // Use empty array since we're using attendance module holidays
        version: `v${Date.now()}`, // Generate version based on timestamp
        effectiveDate: new Date().toISOString(),
        isActive: true,
        createdBy: 1, // TODO: Get from current user context
        companyId: 1 // TODO: Get from company context
      };

      // Save complete policy configuration
      const policyResponse = await leavePolicyApi.create(policyConfiguration);
      if (!policyResponse.success) {
        throw new Error(policyResponse.error || 'Failed to save policy configuration');
      }

      // Prepare data for parent component callback
      const policyData: LeavePolicyData = {
        leaveTypes
      };
      
      onSave?.(policyData);
      showNotification('success', 'Leave policies saved successfully!');
      setIsDirty(false);
    } catch (error) {
      console.error('Error saving leave policies:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save leave policies. Please check your connection.';
      showNotification('error', errorMessage);
    }
  };

  const updateLeaveType = async (updatedLeaveType: LeaveTypePolicy) => {
    setLeaveTypes(prev => {
      const existingIndex = prev.findIndex(lt => lt.leaveType === updatedLeaveType.leaveType);
      if (existingIndex >= 0) {
        // Update existing leave type
        return prev.map(lt => lt.leaveType === updatedLeaveType.leaveType ? updatedLeaveType : lt);
      } else {
        // Add new leave type
        return [...prev, updatedLeaveType];
      }
    });
    setIsDirty(true);

    // Save the individual leave type to the database
    try {
      // Check if this is a new leave type (starts with NEW_TYPE_) or has a temporary timestamp ID
      const isNewLeaveType = updatedLeaveType.leaveType.startsWith('NEW_TYPE_') || 
                            !updatedLeaveType.id || 
                            updatedLeaveType.id > Date.now() - 86400000; // If ID is from last 24 hours, it's likely a timestamp
      
      if (!isNewLeaveType && updatedLeaveType.id && typeof updatedLeaveType.id === 'number' && updatedLeaveType.id > 0) {
        // Update existing leave type (has a real database ID)
        console.log('Updating existing leave type with ID:', updatedLeaveType.id);
        const response = await leaveTypesApi.update(updatedLeaveType.id, updatedLeaveType);
        if (response.success) {
          showNotification('success', 'Leave type updated successfully!');
        } else {
          // Handle update failure properly without creating duplicates
          console.error('Failed to update leave type:', response.error);
          showNotification('error', response.error || 'Failed to update leave type');
          
          // Revert local state changes if update failed
          await loadLeavePolicies();
        }
      } else {
        // Create new leave type
        console.log('Creating new leave type:', updatedLeaveType.leaveType);
        
        // Remove the temporary timestamp ID before sending to server
        const { id, ...leaveTypeWithoutId } = updatedLeaveType;
        
        const response = await leaveTypesApi.create(leaveTypeWithoutId);
        if (response.success) {
          // Update the local state with the created leave type (including the real database ID)
          setLeaveTypes(prev => 
            prev.map(lt => 
              lt.leaveType === updatedLeaveType.leaveType ? response.data : lt
            )
          );
          showNotification('success', 'Leave type created successfully!');
        } else {
          console.error('Failed to create leave type:', response.error);
          showNotification('error', response.error || 'Failed to create leave type');
          
          // Revert local state changes if creation failed
          await loadLeavePolicies();
        }
      }
    } catch (error) {
      console.error('Error saving leave type:', error);
      showNotification('error', 'Failed to save leave type. Please try again.');
      
      // Revert local state changes if there was an exception
      await loadLeavePolicies();
    }
  };

  const deleteLeaveType = async (leaveTypeToDelete: string) => {
    const leaveTypeToDeleteObj = leaveTypes.find(lt => lt.leaveType === leaveTypeToDelete);
    
    if (!leaveTypeToDeleteObj) {
      showNotification('error', 'Leave type not found');
      return;
    }

    // Remove from local state first
    setLeaveTypes(prev => prev.filter(lt => lt.leaveType !== leaveTypeToDelete));
    setIsDirty(true);

    // Delete from database if it has a valid ID
      try {
      if (leaveTypeToDeleteObj.id && typeof leaveTypeToDeleteObj.id === 'number' && leaveTypeToDeleteObj.id > 0) {
        const response = await leaveTypesApi.delete(leaveTypeToDeleteObj.id);
        if (response.success) {
          showNotification('success', 'Leave type deleted successfully!');
        } else {
          console.error('Failed to delete leave type:', response.error);
          showNotification('error', response.error || 'Failed to delete leave type');
          
          // Revert local state changes if deletion failed
          await loadLeavePolicies();
        }
      } else {
        // For leave types without valid IDs, just show success since they were only local
        showNotification('success', 'Leave type removed successfully!');
        }
      } catch (error) {
        console.error('Error deleting leave type:', error);
        showNotification('error', 'Failed to delete leave type. Please try again.');
      
      // Revert local state changes if there was an exception
      await loadLeavePolicies();
    }
  };

    const addNewLeaveType = () => {
    // Generate unique leave type with shorter name
    const timestamp = Date.now();
    const shortId = timestamp.toString().slice(-8); // Use last 8 digits
    const newLeaveType: LeaveTypePolicy = {
      id: timestamp, // Use timestamp as temporary ID
      leaveType: `NEW_TYPE_${shortId}`, // Shorter to fit varchar(50)
      enabled: true,
      displayName: '',
      description: '',
      settings: {
        minDaysNotice: 0,
        allowLeaveModification: false,
        enableProratedLeave: false
      },
      applicableRoles: [],
      applicableDepartments: [],
      maxDaysPerYear: 0,
      minServicePeriod: 0,
      allowCarryForward: false,
      carryForwardLimit: 0,
      encashmentAllowed: false,
      documentRequired: false,
      color: '',
      sortOrder: 0,
      isActive: true,
      category: '',
      genderEligibility: 'all',
      effectiveFrom: '',
      validUntil: ''
    };
    
    setSelectedLeaveType(newLeaveType);
    setShowLeaveTypeModal(true);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div className="flex items-center">
          <Settings className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">Leave Policy Configuration</h2>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`p-4 border-l-4 ${
          notification.type === 'success' ? 'bg-green-50 border-green-400 text-green-700' :
          notification.type === 'error' ? 'bg-red-50 border-red-400 text-red-700' :
          'bg-blue-50 border-blue-400 text-blue-700'
        }`}>
          <div className="flex items-center">
            {notification.type === 'success' ? <CheckCircle className="h-5 w-5 mr-2" /> :
             notification.type === 'error' ? <AlertCircle className="h-5 w-5 mr-2" /> :
             <Info className="h-5 w-5 mr-2" />}
            {notification.message}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'leaveTypes', label: 'Leave Types', icon: Calendar },
            { id: 'versions', label: 'Policy History', icon: Workflow }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'leaveTypes' && (
          <LeaveTypesList
            leaveTypes={leaveTypes}
            onUpdate={updateLeaveType}
            onAdd={addNewLeaveType}
            onEdit={(leaveType) => {
              // Ensure settings object exists with default values
              const leaveTypeWithSettings = {
                ...leaveType,
                settings: {
                  minDaysNotice: 1,
                  allowLeaveModification: false,
                  enableProratedLeave: false,
                  ...leaveType.settings // Override with existing settings if they exist
                }
              };
              setSelectedLeaveType(leaveTypeWithSettings);
              setShowLeaveTypeModal(true);
            }}
            onDelete={deleteLeaveType}
            showNotification={showNotification}
          />
        )}

        {activeTab === 'versions' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Policy Version History</h3>
              <p className="text-sm text-gray-600 mb-6">
                Track changes to leave policies over time. Each policy change creates a new version.
              </p>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-medium text-gray-900">Recent Policy Changes</h4>
              </div>
              <div className="divide-y divide-gray-200">
                {/* Version 1.2 */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        v1.2 (Current)
                      </span>
                      <span className="ml-3 text-sm font-medium text-gray-900">Leave Policy Simplification</span>
                    </div>
                    <span className="text-sm text-gray-500">Dec 15, 2024</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Simplified leave policy management by focusing on core leave types configuration. Removed complex accrual rules and auto-reset features.
                  </p>
                  <div className="flex items-center text-sm text-gray-500">
                    <span>Changed by: HR Manager</span>
                    <span className="mx-2">•</span>
                    <span>Effective: Jan 1, 2025</span>
                  </div>
                </div>

                {/* Version 1.1 */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        v1.1
                      </span>
                      <span className="ml-3 text-sm font-medium text-gray-900">Sick Leave Documentation Requirement</span>
                    </div>
                    <span className="text-sm text-gray-500">Nov 20, 2024</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Added requirement for medical certificate for sick leave exceeding 3 consecutive days.
                  </p>
                  <div className="flex items-center text-sm text-gray-500">
                    <span>Changed by: System Admin</span>
                    <span className="mx-2">•</span>
                    <span>Effective: Dec 1, 2024</span>
                  </div>
                </div>

                {/* Version 1.0 */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        v1.0
                      </span>
                      <span className="ml-3 text-sm font-medium text-gray-900">Initial Policy Setup</span>
                    </div>
                    <span className="text-sm text-gray-500">Jan 1, 2024</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Initial leave policy configuration with basic annual, sick, and personal leave types.
                  </p>
                  <div className="flex items-center text-sm text-gray-500">
                    <span>Created by: HR Manager</span>
                    <span className="mx-2">•</span>
                    <span>Effective: Jan 1, 2024</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center text-sm text-gray-600">
          {isDirty && (
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-orange-500 mr-1" />
              <span>You have unsaved changes</span>
            </div>
          )}
        </div>
        <div className="flex space-x-3">
          {onCancel && (
        <button
          onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          Cancel
        </button>
          )}
        <button
          onClick={handleSave}
          disabled={!isDirty}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Save className="h-4 w-4 mr-2" />
            Save Changes
        </button>
        </div>
      </div>

      {/* Leave Type Modal */}
      {showLeaveTypeModal && selectedLeaveType && (
        <LeaveTypeModal
          leaveType={selectedLeaveType}
          onSave={(leaveType) => {
            updateLeaveType(leaveType);
            setShowLeaveTypeModal(false);
            setSelectedLeaveType(null);
          }}
          onCancel={() => {
            setShowLeaveTypeModal(false);
            setSelectedLeaveType(null);
          }}
          showNotification={showNotification}
        />
      )}
    </div>
  );
};

// Leave Types Tab Component
const LeaveTypesList: React.FC<{
  leaveTypes: LeaveTypePolicy[];
  onUpdate: (leaveType: LeaveTypePolicy) => void;
  onAdd: () => void;
  onEdit: (leaveType: LeaveTypePolicy) => void;
  onDelete: (leaveType: string) => void;
  showNotification: (type: 'success' | 'error' | 'info', message: string) => void;
}> = ({ leaveTypes, onUpdate, onAdd, onEdit, onDelete, showNotification }) => {
  return (
    <div className="space-y-6">
              <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Leave Types Configuration</h3>
            <p className="text-sm text-gray-600">Configure different types of leaves available to employees</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onAdd}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Leave Type
            </button>
          </div>
        </div>

      <div className="grid grid-cols-1 gap-4">
        {leaveTypes.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Leave Types</h4>
            <p className="text-gray-600 mb-4">Get started by creating your first leave type</p>
            <button
              onClick={onAdd}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Leave Type
            </button>
          </div>
        ) : (
          leaveTypes.map(leaveType => (
            <div key={leaveType.leaveType} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={leaveType.enabled}
                    onChange={(e) => onUpdate({ ...leaveType, enabled: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3"
                  />
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{leaveType.displayName}</h4>
                    <p className="text-xs text-gray-500">{leaveType.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => onEdit(leaveType)}
                    className="p-2 text-gray-400 hover:text-gray-600"
                    title="Edit leave type"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => {
                      showNotification('info', `Deleting ${leaveType.displayName}...`);
                      onDelete(leaveType.leaveType);
                    }}
                    className="p-2 text-red-400 hover:text-red-600"
                    title="Delete leave type"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Max Days/Year:</span>
                <span className="ml-2 font-medium">{leaveType.maxDaysPerYear}</span>
              </div>
              <div>
                <span className="text-gray-500">Notice Period:</span>
                <span className="ml-2 font-medium">{leaveType.settings.minDaysNotice} days</span>
              </div>
              <div>
                <span className="text-gray-500">Category:</span>
                <span className="ml-2 font-medium capitalize">{(leaveType as any).category || 'annual'}</span>
              </div>
              <div>
                <span className="text-gray-500">Gender:</span>
                <span className="ml-2 font-medium">
                  {(leaveType as any).genderEligibility === 'male' ? '👨‍💼 Male' : 
                   (leaveType as any).genderEligibility === 'female' ? '👩‍💼 Female' : '👥 All'}
                </span>
              </div>
            </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Leave Type Modal Component
const LeaveTypeModal: React.FC<{
  leaveType: LeaveTypePolicy;
  onSave: (leaveType: LeaveTypePolicy) => void;
  onCancel: () => void;
  showNotification: (type: 'success' | 'error' | 'info', message: string) => void;
}> = ({ leaveType, onSave, onCancel, showNotification }) => {
  const [formData, setFormData] = useState<LeaveTypePolicy>(leaveType);
  const [customCategory, setCustomCategory] = useState<string>('');
  
  // Predefined categories
  const predefinedCategories = ['annual', 'sick', 'casual', 'unpaid', 'maternity', 'paternity'];
  
  // Initialize custom category if current category is not predefined
  React.useEffect(() => {
    if (formData.category && !predefinedCategories.includes(formData.category)) {
      setCustomCategory(formData.category);
    }
  }, [formData.category]);
  
  // Generate leave type ID from display name
  const generateLeaveTypeId = (displayName: string): string => {
    if (!displayName.trim()) return '';
    
    return displayName
      .trim()
      .toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
  };
  
  const isNewLeaveType = formData.leaveType.startsWith('NEW_TYPE_');
  const generatedLeaveTypeId = isNewLeaveType ? generateLeaveTypeId(formData.displayName) : formData.leaveType;

  const handleSave = () => {
    // Validation
    if (!formData.displayName.trim()) {
      showNotification('error', 'Please enter a leave type name');
      return;
    }
    
    if (formData.maxDaysPerYear <= 0) {
      showNotification('error', 'Please enter a valid maximum days per year');
      return;
    }
    
    // Generate leaveType from displayName if it's a new leave type
    const isNewLeaveType = formData.leaveType.startsWith('NEW_TYPE_');
    
    let updatedFormData = { ...formData };
    
    if (isNewLeaveType && formData.displayName.trim()) {
      // Convert display name to proper leave type identifier
      const leaveTypeId = generateLeaveTypeId(formData.displayName);
      
      updatedFormData = {
        ...formData,
        leaveType: leaveTypeId || 'CUSTOM_LEAVE'
      };
    }
    
    onSave(updatedFormData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Configure Leave Type</h3>
          <button onClick={onCancel} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Leave Type Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.displayName}
                onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Annual Leave, Sick Leave, etc."
              />
              {isNewLeaveType && (
                <p className="text-xs text-gray-500 mt-1">
                  System ID: <span className="font-mono text-blue-600">{generatedLeaveTypeId || 'Enter name above'}</span>
                </p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Days Per Year <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min="1"
                value={formData.maxDaysPerYear === 0 ? '' : formData.maxDaysPerYear || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, maxDaysPerYear: e.target.value ? parseInt(e.target.value) : 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter maximum days (e.g., 25)"
              />
            </div>
          </div>

          {/* Leave Type Category and Gender Eligibility */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Leave Type Category
              </label>
              <select
                value={
                  formData.category && predefinedCategories.includes(formData.category) 
                    ? formData.category 
                    : formData.category 
                      ? 'custom' 
                      : ''
                }
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  console.log('Selected category:', selectedValue);
                  if (selectedValue === 'custom') {
                    setFormData(prev => ({ ...prev, category: customCategory || 'custom' }));
                  } else {
                    setFormData(prev => ({ ...prev, category: selectedValue }));
                    setCustomCategory('');
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="" disabled>Select leave type category</option>
                <option value="annual">Annual Leave</option>
                <option value="sick">Sick Leave</option>
                <option value="casual">Casual Leave</option>
                <option value="unpaid">Unpaid Leave</option>
                <option value="maternity">Maternity Leave</option>
                <option value="paternity">Paternity Leave</option>
                <option value="custom">Custom Category</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Select the type of leave being configured</p>
              
              {/* Custom Category Input */}
              {(formData.category === 'custom' || (formData.category && !predefinedCategories.includes(formData.category))) && (
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom Category Name
                  </label>
                  <input
                    type="text"
                    value={customCategory}
                    onChange={(e) => {
                      setCustomCategory(e.target.value);
                      setFormData(prev => ({ ...prev, category: e.target.value }));
                    }}
                    placeholder="Enter custom category name..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Enter a custom name for this leave category</p>
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gender Eligibility
              </label>
              <select
                value={formData.genderEligibility || 'all'}
                onChange={(e) => setFormData(prev => ({ ...prev, genderEligibility: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">👥 All Employees</option>
                <option value="male">👨‍💼 Male Only</option>
                <option value="female">👩‍💼 Female Only</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Who can apply for this leave type</p>
            </div>
          </div>

          {/* Effective Date and Expiry Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Effective From
              </label>
              <input
                type="date"
                value={formData.effectiveFrom || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, effectiveFrom: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">When this leave policy becomes active</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valid Until
              </label>
              <input
                type="date"
                value={formData.validUntil || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, validUntil: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Leave blank for no expiry date</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Policy Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Days Notice Required
              </label>
              <input
                type="number"
                min="0"
                value={formData.settings.minDaysNotice}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  settings: { 
                    ...prev.settings, 
                    minDaysNotice: parseInt(e.target.value) || 0 
                  } 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Days in advance required to apply</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Service Period
              </label>
              <input
                type="number"
                min="0"
                value={formData.minServicePeriod === 0 ? '' : formData.minServicePeriod || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, minServicePeriod: e.target.value ? parseInt(e.target.value) : 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Months of service required</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Carry Forward Limit
              </label>
              <input
                type="number"
                min="0"
                value={formData.carryForwardLimit === 0 ? '' : formData.carryForwardLimit || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, carryForwardLimit: e.target.value ? parseInt(e.target.value) : 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={!formData.allowCarryForward}
              />
              <p className="text-xs text-gray-500 mt-1">Maximum days to carry forward</p>
            </div>
          </div>

          {/* Checkboxes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              { key: 'allowCarryForward', label: 'Allow Carry Forward', desc: 'Unused leaves can be carried to next year' },
              { key: 'encashmentAllowed', label: 'Allow Encashment', desc: 'Unused leaves can be encashed' },
              { key: 'documentRequired', label: 'Document Required', desc: 'Supporting documents required for approval' }
            ].map(setting => (
              <div key={setting.key} className="flex items-start">
                <input
                  type="checkbox"
                  id={setting.key}
                  checked={formData[setting.key as keyof LeaveTypePolicy] as boolean}
                  onChange={(e) => setFormData(prev => ({ ...prev, [setting.key]: e.target.checked }))}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="ml-3">
                  <label htmlFor={setting.key} className="text-sm font-medium text-gray-700">
                    {setting.label}
                  </label>
                  <p className="text-xs text-gray-500">{setting.desc}</p>
                </div>
              </div>
            ))}
            
            {/* Allow Leave Modification */}
            <div className="flex items-start">
              <input
                type="checkbox"
                id="allowLeaveModification"
                checked={formData.settings.allowLeaveModification}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  settings: { 
                    ...prev.settings, 
                    allowLeaveModification: e.target.checked 
                  } 
                }))}
                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="allowLeaveModification" className="text-sm font-medium text-gray-700">
                  Allow Leave Modification
                </label>
                <p className="text-xs text-gray-500">Employees can modify submitted leave requests</p>
              </div>
            </div>
          </div>

          {/* Prorated Leave - Simple checkbox */}
          <div className="flex items-start">
            <input
              type="checkbox"
              id="enableProratedLeave"
              checked={formData.settings.enableProratedLeave || false}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                settings: { 
                  ...prev.settings, 
                  enableProratedLeave: e.target.checked 
                } 
              }))}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div className="ml-3">
              <label htmlFor="enableProratedLeave" className="text-sm font-medium text-gray-700">
                Enable Prorated Leave
              </label>
              <p className="text-xs text-gray-500">Calculate proportional leave for mid-year joiners</p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!formData.displayName.trim() || formData.maxDaysPerYear <= 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isNewLeaveType ? 'Create Leave Type' : 'Update Leave Type'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeavePolicyConfiguration; 