import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLeaveTypeColumnLength1703900000001 implements MigrationInterface {
  name = 'UpdateLeaveTypeColumnLength1703900000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('📝 Updating leave_type column length from 50 to 100 characters...');
    
    // Update the column length for leave_type in leave_type_policies table
    await queryRunner.query(`
      ALTER TABLE leave_type_policies 
      ALTER COLUMN leaveType varchar(100)
    `);
    
    console.log('✅ Successfully updated leave_type column length');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('📝 Reverting leave_type column length back to 50 characters...');
    
    // Revert the column length change
    await queryRunner.query(`
      ALTER TABLE leave_type_policies 
      ALTER COLUMN leaveType varchar(50)
    `);
    
    console.log('✅ Successfully reverted leave_type column length');
  }
} 