{"name": "infraspine", "version": "1.0.0", "description": "InfraSpine - Infrastructure Management System", "main": "src/server/index.ts", "scripts": {"dev:server": "ts-node-dev --respawn --transpile-only src/server/index.ts", "dev:client": "vite", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "node dist/server/index.js", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert", "run:migrations": "ts-node src/scripts/runMigrations.ts", "run:printer-migration": "ts-node src/scripts/runPrinterMigration.ts", "run:payment-details-migration": "ts-node src/scripts/runPaymentDetailsMigration.ts", "run:payment-card-details-migration": "ts-node src/scripts/runPaymentCardDetailsMigration.ts", "run:salary-trench-migration": "ts-node src/scripts/runSalaryTrenchMigration.ts", "create:admin": "ts-node src/scripts/createAdmin.ts", "create:categories": "ts-node src/scripts/createDefaultCategories.ts", "create:original-categories": "ts-node src/scripts/createOriginalCategories.ts", "test": "jest", "migrate": "ts-node src/scripts/runMigrations.ts", "migrate:remove-primary-user": "ts-node src/scripts/runRemovePrimaryUserMigration.ts", "migrate:remove-previous-employee-id": "ts-node src/scripts/runRemovePreviousEmployeeIdMigration.ts", "repair-employee-data": "ts-node src/scripts/repairEmployeeData.ts", "cleanup-custom-leave-types": "ts-node src/scripts/cleanupCustomLeaveTypes.ts", "check-leave-types": "ts-node src/scripts/checkLeaveTypes.ts", "fix-orphaned-leave-types": "ts-node src/scripts/fixOrphanedLeaveTypes.ts", "consolidate-leave-types": "ts-node src/scripts/consolidateLeaveTypes.ts", "cleanup-unauthorized-leave-types": "ts-node src/scripts/cleanupUnauthorizedLeaveTypes.ts", "final-leave-types-cleanup": "ts-node src/scripts/finalLeaveTypesCleanup.ts"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@mui/icons-material": "^6.4.11", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^6.4.11", "@mui/x-data-grid": "^8.2.0", "@tanstack/react-table": "^8.21.2", "@types/dompurify": "^3.0.5", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.5.1", "@types/jest": "^29.5.14", "@types/json2csv": "^5.0.7", "@types/jspdf": "^1.3.3", "@types/marked": "^5.0.2", "@types/multer": "^1.4.12", "@types/pdfkit": "^0.13.9", "@types/pdfmake": "^0.2.11", "@types/pg": "^8.11.11", "@types/react-select": "^5.0.0", "@types/socket.io": "^3.0.1", "@types/socket.io-client": "^1.4.36", "axios": "^1.8.1", "bcryptjs": "^2.4.3", "chart.js": "^4.4.8", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dompurify": "^3.2.4", "dotenv": "^16.4.5", "emoji-picker-react": "^4.12.0", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "framer-motion": "^12.4.7", "helmet": "^8.0.0", "jest": "^29.7.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.331.0", "marked": "^15.0.7", "mime-types": "^3.0.1", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.1", "node-cache": "^5.1.2", "node-fetch": "^2.7.0", "pdfkit": "^0.16.0", "pdfmake": "^0.2.19", "pg": "^8.13.3", "react": "^18.2.0", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^4.0.12", "react-hot-toast": "^2.4.1", "react-quill": "^2.0.0", "react-router-dom": "^6.22.1", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "reflect-metadata": "^0.2.1", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "typeorm": "^0.3.20", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.8", "@types/lodash": "^4.17.16", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3", "vite": "^6.3.0"}, "keywords": [], "author": "", "license": "ISC"}