import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class UpdateAssetIdToAutoIncrement1710000000001 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, drop foreign keys that reference the assets table
        const assetMaintenanceTable = await queryRunner.getTable("asset_maintenance");
        if (assetMaintenanceTable) {
            const foreignKeys = assetMaintenanceTable.foreignKeys.filter(
                fk => fk.referencedTableName === "assets"
            );
            
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey("asset_maintenance", foreignKey);
            }
        }

        // Drop the primary key constraint
        await queryRunner.query(`ALTER TABLE assets DROP PRIMARY KEY`);

        // Drop the old id column
        await queryRunner.dropColumn("assets", "id");

        // Add the new auto-incrementing id column
        await queryRunner.addColumn(
            "assets",
            new TableColumn({
                name: "id",
                type: "int",
                isPrimary: true,
                isGenerated: true,
                generationStrategy: "increment"
            })
        );

        // Update the assetId column in asset_maintenance to be an integer
        await queryRunner.query(`
            ALTER TABLE asset_maintenance 
            MODIFY COLUMN assetId INT
        `);

        // Re-create foreign keys with the new column type
        await queryRunner.query(`
            ALTER TABLE asset_maintenance
            ADD CONSTRAINT FK_asset_maintenance_asset
            FOREIGN KEY (assetId) REFERENCES assets(id)
            ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This is a destructive migration, so the down method would be complex
        // and potentially data-losing. It's better to handle this with a backup
        // if needed.
        throw new Error("Cannot reverse this migration");
    }
} 