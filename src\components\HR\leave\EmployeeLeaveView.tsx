import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  PlusCircle, 
  Edit, 
  Check, 
  Search, 
  ArrowLeft,
  ArrowRight,
  Filter,
  TrendingUp,
  Award,
  Info,
  Download,
  ChevronRight
} from 'lucide-react';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrInputStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInfoAlertStyle,
  hrSuccessAlertStyle
} from '../../../styles/hrWorkflow';
import { useAuth } from '../../../contexts/AuthContext';
import employeePortalService, { EmployeeLeaveData } from '../../../services/EmployeePortalService';

// Interface for leave request data
interface LeaveRequest {
  id: number;
  type: string;
  startDate: string;
  endDate: string;
  days: number;
  status: string;
  reason: string;
  approvedBy: string | null;
  appliedOn: string;
  rejectionReason?: string;
}

// Interface for leave balance data
interface LeaveBalance {
  leaveType: string;
  total: number;
  used: number;
  pending: number;
  remaining: number;
  carryForward?: number;
  expiryDate?: string;
}

interface EmployeeLeaveViewProps {
  leaveData?: EmployeeLeaveData | null;
  employeeId?: number;
  employeeName?: string;
}

const EmployeeLeaveView: React.FC<EmployeeLeaveViewProps> = ({ 
  leaveData: propLeaveData, 
  employeeId: propEmployeeId,
  employeeName: propEmployeeName 
}) => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState<'summary' | 'details' | 'apply'>('summary');
  const [selectedLeave, setSelectedLeave] = useState<LeaveRequest | null>(null);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [leaveRecords, setLeaveRecords] = useState<LeaveRequest[]>([]);
  const [filterYear, setFilterYear] = useState<string>(new Date().getFullYear().toString());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const [leaveForm, setLeaveForm] = useState({
    type: '',
    startDate: '',
    endDate: '',
    reason: '',
    contactInfo: ''
  });

  // Use props if provided, otherwise fall back to user data
  const employeeId = propEmployeeId || (user?.id && !isNaN(Number(user.id)) ? Number(user.id) : undefined);
  const employeeName = propEmployeeName || user?.name || 'Employee';

  // Load leave data on component mount
  useEffect(() => {
    loadLeaveData();
  }, [propLeaveData, employeeId]);

  const loadLeaveData = async () => {
    // If leave data is provided as props, use it
    if (propLeaveData) {
      setLeaveBalances(propLeaveData.balances);
      
      // Transform recent requests to match the expected format
      const transformedRecords: LeaveRequest[] = propLeaveData.recentRequests.map(req => ({
        id: req.id,
        type: req.type,
        startDate: req.from,
        endDate: req.to,
        days: req.days,
        status: req.status,
        reason: req.reason || '',
        approvedBy: null,
        appliedOn: new Date().toISOString()
      }));
      
      setLeaveRecords(transformedRecords);
      setLoading(false);
      return;
    }
    
    // Fall back to fetching data if no props provided
    if (!employeeId) {
      console.warn('No employee ID available for leave data');
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const leaveData = await employeePortalService.getEmployeeLeaveData(employeeId);
      
      if (leaveData) {
        setLeaveBalances(leaveData.balances);
        
        // Transform recent requests to match the expected format
        const transformedRecords: LeaveRequest[] = leaveData.recentRequests.map(req => ({
          id: req.id,
          type: req.type,
          startDate: req.from,
          endDate: req.to,
          days: req.days,
          status: req.status,
          reason: req.reason || '',
          approvedBy: null,
          appliedOn: new Date().toISOString()
        }));
        
        setLeaveRecords(transformedRecords);
      }
    } catch (error) {
      console.error('Error loading leave data:', error);
    } finally {
      setLoading(false);
    }
  };



  // Helper function to get leave balance by type
  const getLeaveBalance = (leaveType: string) => {
    // Map common leave type names to actual database values
    const typeMapping: { [key: string]: string } = {
      'annual': 'ANNUAL_LEAVE',
      'sick': 'SICK_LEAVE', 
      'casual': 'CASUAL_LEAVE'
    };
    
    const actualType = typeMapping[leaveType.toLowerCase()] || leaveType;
    const balance = leaveBalances.find(b => 
      b.leaveType === actualType || b.leaveType.toLowerCase().includes(leaveType.toLowerCase())
    );
    
    return balance || { 
      leaveType, 
      total: 0, 
      used: 0, 
      pending: 0, 
      remaining: 0 
    };
  };

  // Filter leave records by year
  const filteredRecords = leaveRecords.filter((record: LeaveRequest) => 
    record.startDate.startsWith(filterYear)
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      weekday: 'short'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge with enhanced styling
  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold tracking-wide uppercase transition-all duration-200";
    
    switch (status.toLowerCase()) {
      case 'approved':
        return (
          <span className={`${baseClasses} bg-emerald-100 text-emerald-800 border border-emerald-200`}>
            <CheckCircle className="h-3 w-3 mr-1.5" />
            Approved
          </span>
        );
      case 'pending':
        return (
          <span className={`${baseClasses} bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700`}>
            <Clock className="h-3 w-3 mr-1.5" />
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800 border border-red-200`}>
            <AlertCircle className="h-3 w-3 mr-1.5" />
            Rejected
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`}>
            <Info className="h-3 w-3 mr-1.5" />
            {status}
          </span>
        );
    }
  };

  // Calculate usage percentage for progress bars
  const getUsagePercentage = (used: number, total: number) => {
    return total > 0 ? Math.round((used / total) * 100) : 0;
  };

  // Professional loading component
  const LoadingSpinner = () => (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-100 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
          <div className="absolute inset-0 w-16 h-16 border-2 border-blue-200 rounded-full mx-auto opacity-30"></div>
        </div>
                 <p className="mt-4 text-sm text-gray-600 font-medium">Loading your leave information...</p>
         <p className="text-xs text-gray-500 mt-1">Please wait while we fetch your data</p>
      </div>
    </div>
  );

  // Enhanced summary stats component
  const SummaryStats = () => {
    const totalAllocated = leaveBalances.reduce((sum, b) => sum + b.total, 0);
    const totalUsed = leaveBalances.reduce((sum, b) => sum + b.used, 0);
    const totalRemaining = leaveBalances.reduce((sum, b) => sum + b.remaining, 0);
    const usagePercentage = totalAllocated > 0 ? Math.round((totalUsed / totalAllocated) * 100) : 0;

         return (
       <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
         <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">Total Allocated</p>
               <p className="text-lg font-bold text-blue-900 dark:text-blue-100 mt-1">{totalAllocated}</p>
               <p className="text-xs text-blue-700 dark:text-blue-300">days this year</p>
             </div>
             <div className="p-2 bg-blue-600 dark:bg-blue-700 rounded-lg">
               <Calendar className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

                 <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 border border-emerald-200 dark:border-emerald-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-emerald-600 dark:text-emerald-400 uppercase tracking-wide">Remaining</p>
               <p className="text-lg font-bold text-emerald-900 dark:text-emerald-100 mt-1">{totalRemaining}</p>
               <p className="text-xs text-emerald-700 dark:text-emerald-300">days available</p>
             </div>
             <div className="p-2 bg-emerald-600 dark:bg-emerald-700 rounded-lg">
               <TrendingUp className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

         <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border border-amber-200 dark:border-amber-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-amber-600 dark:text-amber-400 uppercase tracking-wide">Used</p>
               <p className="text-lg font-bold text-amber-900 dark:text-amber-100 mt-1">{totalUsed}</p>
               <p className="text-xs text-amber-700 dark:text-amber-300">days consumed</p>
             </div>
             <div className="p-2 bg-amber-600 dark:bg-amber-700 rounded-lg">
               <CheckCircle className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

         <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">Usage Rate</p>
               <p className="text-lg font-bold text-purple-900 dark:text-purple-100 mt-1">{usagePercentage}%</p>
               <p className="text-xs text-purple-700 dark:text-purple-300">utilization</p>
             </div>
             <div className="p-2 bg-purple-600 dark:bg-purple-700 rounded-lg">
               <Award className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>
      </div>
    );
  };

  // Professional header component
     const HeaderSection = () => (
     <div className="mb-4">
       <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
         <div className="flex items-center space-x-3">
           <div className="flex items-center space-x-3">
             <div className="p-3 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg shadow-sm dark:from-blue-700 dark:to-blue-800">
               <Calendar className="h-6 w-6 text-white" />
             </div>
             <div>
               <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Leave Management</h1>
               <p className="text-sm text-gray-600 dark:text-gray-400">Manage your time off and track leave balances</p>
             </div>
           </div>
         </div>
         
         <div className="flex items-center space-x-3">
           <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-2 shadow-sm">
             <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
             <select
               className="border-0 bg-transparent text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none"
               value={filterYear}
               onChange={(e) => setFilterYear(e.target.value)}
             >
               <option value="2025">2025</option>
               <option value="2024">2024</option>
               <option value="2023">2023</option>
             </select>
           </div>
           
           <button
             onClick={() => setCurrentView('apply')}
             className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-800 dark:hover:to-blue-900 transition-all duration-200 shadow-lg hover:shadow-xl"
           >
             <PlusCircle className="h-4 w-4" />
             <span className="text-sm font-semibold">Apply Leave</span>
           </button>
         </div>
       </div>
     </div>
   );

  // Enhanced leave balance cards
     const LeaveBalanceCards = () => (
     <div className="mb-4">
       <div className="flex items-center justify-between mb-3">
         <h2 className="text-base font-semibold text-gray-900 dark:text-gray-100">Leave Balances</h2>
         <div className="text-xs text-gray-500 dark:text-gray-400">Updated {new Date().toLocaleDateString()}</div>
       </div>
       
       {leaveBalances.length === 0 ? (
         <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
           <Calendar className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
           <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-2">No Leave Balances Available</h3>
           <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
             Your leave balances have not been configured yet. Please contact your HR department for assistance with setting up your leave entitlements.
           </p>
         </div>
       ) : (
         <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-3">
          {leaveBalances.map((balance, index) => {
            const getLeaveColor = (leaveType: string) => {
              if (leaveType.includes('ANNUAL')) return { 
                gradient: 'from-blue-500 to-blue-600', 
                bg: 'bg-blue-50 dark:bg-blue-900/20', 
                border: 'border-blue-200 dark:border-blue-700',
                text: 'text-blue-900 dark:text-blue-100',
                progress: 'bg-blue-500'
              };
              if (leaveType.includes('SICK')) return { 
                gradient: 'from-emerald-500 to-emerald-600', 
                bg: 'bg-emerald-50 dark:bg-emerald-900/20', 
                border: 'border-emerald-200 dark:border-emerald-700',
                text: 'text-emerald-900 dark:text-emerald-100',
                progress: 'bg-emerald-500'
              };
              if (leaveType.includes('CASUAL')) return { 
                gradient: 'from-purple-500 to-purple-600', 
                bg: 'bg-purple-50 dark:bg-purple-900/20', 
                border: 'border-purple-200 dark:border-purple-700',
                text: 'text-purple-900 dark:text-purple-100',
                progress: 'bg-purple-500'
              };
              return { 
                gradient: 'from-gray-500 to-gray-600', 
                bg: 'bg-gray-50 dark:bg-gray-900/20', 
                border: 'border-gray-200 dark:border-gray-700',
                text: 'text-gray-900 dark:text-gray-100',
                progress: 'bg-gray-500'
              };
            };
            
            const colors = getLeaveColor(balance.leaveType);
            const displayName = balance.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
            const usagePercentage = getUsagePercentage(balance.used, balance.total);
            const remainingPercentage = getUsagePercentage(balance.remaining, balance.total);
            
                         return (
               <div key={index} className={`bg-white dark:bg-gray-800 ${colors.border} border rounded-lg p-3 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1`}>
                 <div className="flex items-center justify-between mb-2">
                   <div className={`px-2 py-1 ${colors.bg} rounded-lg`}>
                     <h3 className={`font-semibold ${colors.text} text-xs uppercase tracking-wide`}>{displayName}</h3>
                   </div>
                   <div className={`p-1 bg-gradient-to-r ${colors.gradient} rounded-lg shadow-sm`}>
                     <Calendar className="h-3 w-3 text-white" />
                   </div>
                 </div>
                 
                 <div className="space-y-2">
                   <div className="text-center">
                     <div className={`text-lg font-bold ${colors.text} mb-1`}>{balance.remaining}</div>
                     <div className="text-xs text-gray-600 dark:text-gray-400">days remaining</div>
                   </div>
                   
                   <div className="space-y-2">
                     <div className="flex justify-between text-xs">
                       <span className="text-gray-600 dark:text-gray-400">Used</span>
                       <span className="font-semibold text-gray-900 dark:text-gray-100">{balance.used} / {balance.total}</span>
                     </div>
                     
                     <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                       <div 
                         className={`${colors.progress} h-1 rounded-full transition-all duration-500`}
                         style={{ width: `${usagePercentage}%` }}
                       ></div>
                     </div>
                     
                     {balance.pending > 0 && (
                       <div className="flex justify-between text-xs">
                         <span className="text-amber-600 dark:text-amber-400">Pending</span>
                         <span className="font-semibold text-amber-700 dark:text-amber-300">{balance.pending} days</span>
                       </div>
                     )}
                   </div>
                 </div>
               </div>
             );
          })}
        </div>
      )}
    </div>
  );

  // Handle leave application form submission
  const handleLeaveSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!leaveForm.type) {
      alert('Please select a leave type');
      return;
    }
    
    if (!leaveForm.reason.trim()) {
      alert('Please provide a reason for your leave');
      return;
    }
    
    if (!leaveForm.startDate || !leaveForm.endDate) {
      alert('Please select start and end dates');
      return;
    }

    // Create new request
    const startDate = new Date(leaveForm.startDate);
    const endDate = new Date(leaveForm.endDate);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    
    const newLeave = {
      id: Math.max(...leaveRecords.map(r => r.id), 0) + 1,
      type: leaveForm.type,
      startDate: leaveForm.startDate,
      endDate: leaveForm.endDate,
      days: diffDays,
      status: 'pending',
      reason: leaveForm.reason,
      approvedBy: null,
      appliedOn: new Date().toISOString()
    };
    
    setLeaveRecords([...leaveRecords, newLeave]);
    setSuccessMessage('Your leave application has been submitted successfully and is now pending approval.');
    setCurrentView('summary');
    
    // Reset form
    setLeaveForm({
      type: '',
      startDate: '',
      endDate: '',
      reason: '',
      contactInfo: ''
    });
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  // Render summary view with enhanced design
     const renderSummaryView = () => (
     <div className="space-y-4">
       <HeaderSection />
       
       {/* Success message */}
       {successMessage && (
         <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 rounded-lg p-3 shadow-sm">
           <div className="flex items-center">
             <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400 mr-2 flex-shrink-0" />
             <div>
               <h4 className="text-emerald-900 dark:text-emerald-100 font-semibold text-sm">Success!</h4>
               <p className="text-emerald-800 dark:text-emerald-300 text-xs mt-1">{successMessage}</p>
             </div>
           </div>
         </div>
       )}

       <SummaryStats />
       <LeaveBalanceCards />

       {/* Leave History */}
       <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
         <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
           <div className="flex items-center justify-between">
             <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Leave History ({filterYear})</h3>
             <div className="text-xs text-gray-500 dark:text-gray-400">{filteredRecords.length} records</div>
           </div>
         </div>
        
                 <div className="overflow-x-auto">
           <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
             <thead className="bg-gray-50 dark:bg-gray-900">
               <tr>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Leave Type</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Duration</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Days</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Applied</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Status</th>
                 <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Actions</th>
               </tr>
             </thead>
             <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredRecords.length === 0 ? (
                                 <tr>
                   <td colSpan={6} className="px-6 py-8 text-center">
                     <div className="flex flex-col items-center">
                       <FileText className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
                       <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-2">No Leave Records Found</h4>
                       <p className="text-gray-600 dark:text-gray-400 max-w-md">
                         You haven't applied for any leave in {filterYear}. Click "Apply Leave" to submit your first request.
                       </p>
                     </div>
                   </td>
                 </tr>
              ) : (
                filteredRecords.map(record => (
                                     <tr 
                     key={record.id}
                     className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer"
                     onClick={() => {
                       setSelectedLeave(record);
                       setCurrentView('details');
                     }}
                   >
                     <td className="px-3 py-2 whitespace-nowrap">
                       <div className="flex items-center">
                         <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-2">
                           <Calendar className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                         </div>
                         <div>
                           <div className="text-xs font-semibold text-gray-900 dark:text-gray-100">
                             {record.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                           </div>
                         </div>
                       </div>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300">
                       <div>
                         <div className="font-medium">{formatDate(record.startDate)}</div>
                         <div className="text-gray-500 dark:text-gray-400">to {formatDate(record.endDate)}</div>
                       </div>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap">
                       <span className="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-semibold text-gray-800 dark:text-gray-200">
                         {record.days} {record.days === 1 ? 'day' : 'days'}
                       </span>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300">
                       {new Date(record.appliedOn).toLocaleDateString()}
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap">
                       {getStatusBadge(record.status)}
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-right">
                       <button className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200">
                         <ChevronRight className="h-4 w-4" />
                       </button>
                     </td>
                   </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Enhanced details view (continuation in next message due to length)
  const renderDetailsView = () => {
    if (!selectedLeave) return null;

         return (
       <div className="space-y-3">
         <div className="flex items-center justify-between">
                    <button
           onClick={() => setCurrentView('summary')}
           className="flex items-center space-x-1 px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
         >
           <ArrowLeft className="h-3 w-3" />
           <span className="text-xs font-medium">Back to Leave History</span>
         </button>
         
         <h1 className="text-base font-semibold text-gray-900 dark:text-gray-100">Leave Request Details</h1>
         </div>

         <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
           <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
             <div className="flex items-center justify-between">
               <div>
                 <h2 className="text-base font-semibold text-blue-900 dark:text-blue-100">
                   {selectedLeave.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                 </h2>
                 <p className="text-blue-700 dark:text-blue-300 text-xs">Request ID: #{selectedLeave.id}</p>
               </div>
               {getStatusBadge(selectedLeave.status)}
             </div>
           </div>
           
           <div className="p-4 space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                                     <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Duration</label>
                  <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border dark:border-gray-600">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="text-sm text-gray-600 dark:text-gray-400">Start Date</div>
                        <div className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(selectedLeave.startDate)}</div>
                      </div>
                      <div className="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
                      <div className="text-center">
                        <div className="text-sm text-gray-600 dark:text-gray-400">End Date</div>
                        <div className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(selectedLeave.endDate)}</div>
                      </div>
                    </div>
                    <div className="text-center mt-4">
                      <span className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-lg font-semibold">
                        <Clock className="h-4 w-4 mr-2" />
                        {selectedLeave.days} {selectedLeave.days === 1 ? 'Day' : 'Days'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                                     <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Application Details</label>
                  <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border dark:border-gray-600">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Applied On:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">{new Date(selectedLeave.appliedOn).toLocaleDateString()}</span>
                      </div>
                      {selectedLeave.approvedBy && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Approved By:</span>
                          <span className="font-semibold text-gray-900 dark:text-gray-100">{selectedLeave.approvedBy}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
                             <label className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Reason for Leave</label>
              <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border dark:border-gray-600">
                <p className="text-gray-900 dark:text-gray-100 leading-relaxed">{selectedLeave.reason}</p>
              </div>
            </div>

            {selectedLeave.rejectionReason && (
              <div>
                                 <label className="text-xs font-medium text-red-600 dark:text-red-400 uppercase tracking-wide">Rejection Reason</label>
                <div className="mt-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
                  <p className="text-red-800 dark:text-red-300">{selectedLeave.rejectionReason}</p>
                </div>
              </div>
            )}
            
            {selectedLeave.status === 'pending' && (
              <div className="border-t dark:border-gray-700 pt-6">
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to cancel this leave request?')) {
                        setLeaveRecords(leaveRecords.filter(record => record.id !== selectedLeave.id));
                        setSuccessMessage('Your leave request has been cancelled successfully.');
                        setCurrentView('summary');
                      }
                    }}
                    className="px-6 py-2 border border-red-300 dark:border-red-600 text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200 font-medium"
                  >
                    Cancel Request
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Enhanced apply view with professional form design (continued in next message)
     const renderApplyView = () => (
     <div className="space-y-3">
       <div className="flex items-center justify-between">
         <button
           onClick={() => setCurrentView('summary')}
           className="flex items-center space-x-1 px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
         >
           <ArrowLeft className="h-3 w-3" />
           <span className="text-sm font-medium">Back to Dashboard</span>
         </button>
         
         <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Apply for Leave</h1>
       </div>
       
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
         <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
           <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">New Leave Request</h2>
           <p className="text-blue-700 dark:text-blue-300 text-sm">Please fill out all required information for your leave request</p>
         </div>
         
         <form onSubmit={handleLeaveSubmit} className="p-4 space-y-4">
           <div>
             <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Leave Type <span className="text-red-500">*</span>
             </label>
             <select
               id="type"
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100"
               value={leaveForm.type}
               onChange={(e) => setLeaveForm({...leaveForm, type: e.target.value})}
               required
             >
               <option value="">Select Leave Type</option>
               {leaveBalances.map((balance) => (
                 <option key={balance.leaveType} value={balance.leaveType}>
                   {balance.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                   {balance.remaining > 0 ? ` (${balance.remaining} days available)` : ' (No days available)'}
                 </option>
               ))}
             </select>
           </div>
          
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
             <div>
               <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
                 Start Date <span className="text-red-500">*</span>
               </label>
               <input
                 type="date"
                 id="startDate"
                 className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                 value={leaveForm.startDate}
                 onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                 min={new Date().toISOString().split('T')[0]}
                 required
               />
             </div>
             
             <div>
               <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
                 End Date <span className="text-red-500">*</span>
               </label>
               <input
                 type="date"
                 id="endDate"
                 className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                 value={leaveForm.endDate}
                 onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                 min={leaveForm.startDate || new Date().toISOString().split('T')[0]}
                 required
               />
             </div>
           </div>
           
           <div>
             <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Reason for Leave <span className="text-red-500">*</span>
             </label>
             <textarea
               id="reason"
               rows={3}
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
               value={leaveForm.reason}
               onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
               placeholder="Please provide a detailed reason for your leave request..."
               required
             />
           </div>
           
           <div>
             <label htmlFor="contactInfo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Emergency Contact Information
             </label>
             <input
               type="text"
               id="contactInfo"
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
               value={leaveForm.contactInfo}
               onChange={(e) => setLeaveForm({...leaveForm, contactInfo: e.target.value})}
               placeholder="Phone number or email where you can be reached during leave..."
             />
           </div>
          
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                                 <p className="text-sm text-blue-800 dark:text-blue-100 font-medium">
                   Important Information
                 </p>
                 <p className="text-sm text-blue-700 dark:text-blue-300">
                   Your request will be sent to your manager for approval. Please ensure all details are accurate as changes may require resubmission.
                 </p>
                 {leaveForm.type && (
                   <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                     Available Balance: {leaveBalances.find(b => b.leaveType === leaveForm.type)?.remaining || 0} days of {leaveForm.type.replace(/_/g, ' ').toLowerCase()}
                   </p>
                 )}
              </div>
            </div>
          </div>
          
                     <div className="flex justify-end space-x-2 pt-3 border-t border-gray-200 dark:border-gray-700">
             <button
               type="button"
               onClick={() => setCurrentView('summary')}
               className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 text-sm font-medium"
             >
               Cancel
             </button>
             <button
               type="submit"
               className="flex items-center space-x-1 px-5 py-2 bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-800 dark:hover:to-blue-900 transition-all duration-200 shadow-lg hover:shadow-xl text-sm font-semibold"
             >
               <Check className="h-4 w-4" />
               <span>Submit Request</span>
             </button>
           </div>
        </form>
      </div>
    </div>
  );

  // Render the appropriate view based on current state
  if (loading) {
    return <LoadingSpinner />;
  }

      return (
      <div className="p-2 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="w-full">
          {currentView === 'summary' && renderSummaryView()}
          {currentView === 'details' && renderDetailsView()}
          {currentView === 'apply' && renderApplyView()}
        </div>
      </div>
    );
};

export default EmployeeLeaveView; 