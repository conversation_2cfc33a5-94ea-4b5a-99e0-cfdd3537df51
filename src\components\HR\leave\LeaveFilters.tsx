import React from 'react';
import { Search, Filter, RotateCcw } from 'lucide-react';

interface LeaveFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  departmentFilter: string;
  setDepartmentFilter: (value: string) => void;
  leaveTypeFilter: string;
  setLeaveTypeFilter: (value: string) => void;
  dateRangeFilter: string;
  setDateRangeFilter: (value: string) => void;
  startDateFilter: string;
  setStartDateFilter: (value: string) => void;
  endDateFilter: string;
  setEndDateFilter: (value: string) => void;
  priorityFilter: string;
  setPriorityFilter: (value: string) => void;
  positionFilter?: string;
  setPositionFilter?: (value: string) => void;
  balanceStatusFilter?: string;
  setBalanceStatusFilter?: (value: string) => void;
  usageFilter?: string;
  setUsageFilter?: (value: string) => void;
  departments: string[];
  positions?: string[];
  filteredCount: number;
  totalCount: number;
  filterType: 'requests' | 'employees' | 'allocations';
  onResetFilters: () => void;
}

const departmentNames: Record<string, string> = {
  'IT': 'Information Technology',
  'HR': 'Human Resources',
  'FINANCE': 'Finance & Accounting',
  'MARKETING': 'Marketing & Communications',
  'SALES': 'Sales',
  'DIGITAL SALES': 'Digital Sales',
  'OPERATIONS': 'Operations',
  'CSD': 'Customer Service Department',
  'LAND': 'Land Department',
  'LEGAL': 'Legal',
  'MANAGEMENT': 'Management',
  'PND': 'Planning and Development'
};

const LeaveFilters: React.FC<LeaveFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  departmentFilter,
  setDepartmentFilter,
  leaveTypeFilter,
  setLeaveTypeFilter,
  dateRangeFilter,
  setDateRangeFilter,
  startDateFilter,
  setStartDateFilter,
  endDateFilter,
  setEndDateFilter,
  priorityFilter,
  setPriorityFilter,
  positionFilter,
  setPositionFilter,
  balanceStatusFilter,
  setBalanceStatusFilter,
  usageFilter,
  setUsageFilter,
  departments,
  positions = [],
  filteredCount,
  totalCount,
  filterType,
  onResetFilters
}) => {
  const getLeaveTypeLabel = (leaveType: string) => {
    const labels = {
      'ANNUAL': 'Annual Leave',
      'SICK': 'Sick Leave',
      'PERSONAL': 'Personal Leave',
      'UNPAID': 'Unpaid Leave',
      'MATERNITY': 'Maternity Leave',
      'PATERNITY': 'Paternity Leave',
      'BEREAVEMENT': 'Bereavement Leave',
      'COMP_OFF': 'Compensatory Off',
      'OTHER': 'Other'
    };
    return labels[leaveType] || leaveType;
  };

  const renderRequestsFilters = () => (
    <div className="flex flex-col lg:flex-row lg:items-end lg:space-x-2 space-y-2 lg:space-y-0">
      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Search</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Employee name or reason..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-1.5 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors"
          />
        </div>
      </div>
      
      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Department</label>
        <select
          value={departmentFilter}
          onChange={(e) => setDepartmentFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Departments</option>
          {departments.map((dept) => (
            <option key={dept} value={dept}>{departmentNames[dept] || dept}</option>
          ))}
        </select>
      </div>

      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Leave Type</label>
        <select
          value={leaveTypeFilter}
          onChange={(e) => setLeaveTypeFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Types</option>
          {['ANNUAL', 'SICK', 'PERSONAL', 'UNPAID', 'MATERNITY', 'PATERNITY', 'BEREAVEMENT', 'COMP_OFF', 'OTHER'].map((type) => (
            <option key={type} value={type}>{getLeaveTypeLabel(type).replace(' Leave', '')}</option>
          ))}
        </select>
      </div>

      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Status</option>
          <option value="pending">🟡 Pending</option>
          <option value="approved">🟢 Approved</option>
          <option value="rejected">🔴 Rejected</option>
        </select>
      </div>

      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Priority</label>
        <select
          value={priorityFilter}
          onChange={(e) => setPriorityFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Priority</option>
          <option value="urgent">🔴 Urgent (5+ days)</option>
          <option value="normal">🟡 Normal (2-4 days)</option>
          <option value="low">🟢 Low (1 day)</option>
        </select>
      </div>

      <div className="flex-1">
        <label className="block text-xs font-medium text-gray-700 mb-1">Date Range</label>
        <select
          value={dateRangeFilter}
          onChange={(e) => setDateRangeFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Dates</option>
          <option value="today">📅 Today</option>
          <option value="this_week">📅 This Week</option>
          <option value="this_month">📅 This Month</option>
          <option value="last_month">📅 Last Month</option>
          <option value="custom">📅 Custom Range</option>
        </select>
      </div>
    </div>
  );

  const renderEmployeeFilters = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
      <div className="lg:col-span-2">
        <label className="block text-xs font-medium text-gray-700 mb-1">Search</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Name, department, position..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-1.5 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Department</label>
        <select
          value={departmentFilter}
          onChange={(e) => setDepartmentFilter(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Departments</option>
          {departments.map((dept) => (
            <option key={dept} value={dept}>{departmentNames[dept] || dept}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Position</label>
        <select
          value={positionFilter}
          onChange={(e) => setPositionFilter?.(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Positions</option>
          {positions.map((position) => (
            <option key={position} value={position}>👤 {position}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">Balance Status</label>
        <select
          value={balanceStatusFilter}
          onChange={(e) => setBalanceStatusFilter?.(e.target.value)}
          className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
        >
          <option value="all">All Balance Status</option>
          <option value="low_balance">🟡 Low Balance (≤5 days)</option>
          <option value="no_balance">🔴 No Balance (0 days)</option>
          <option value="full_balance">🟢 Full Balance (unused)</option>
        </select>
      </div>

      {filterType === 'employees' && (
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Usage Level</label>
          <select
            value={usageFilter}
            onChange={(e) => setUsageFilter?.(e.target.value)}
            className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
          >
            <option value="all">All Usage Levels</option>
            <option value="high_usage">🔴 High Usage (≥70%)</option>
            <option value="medium_usage">🟡 Medium Usage (30-69%)</option>
            <option value="low_usage">🟢 Low Usage (1-29%)</option>
            <option value="no_usage">⚪ No Usage (0%)</option>
          </select>
        </div>
      )}
    </div>
  );

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-1 shadow-sm mb-0">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-semibold text-gray-900 flex items-center">
          <Filter className="h-4 w-4 mr-2 text-gray-500" />
          Filter & Search Options
        </h4>
        <button
          onClick={onResetFilters}
          className="text-xs text-blue-600 hover:text-blue-800 font-medium flex items-center px-2 py-1 rounded hover:bg-blue-50 transition-colors"
        >
          <RotateCcw className="h-3 w-3 mr-1" />
          Reset All
        </button>
      </div>

      <div className="space-y-2">
        {filterType === 'requests' ? renderRequestsFilters() : renderEmployeeFilters()}

        {/* Custom Date Range */}
        {dateRangeFilter === 'custom' && (
          <div className="border-t border-gray-200 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Start Date</label>
                <input
                  type="date"
                  value={startDateFilter}
                  onChange={(e) => setStartDateFilter(e.target.value)}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">End Date</label>
                <input
                  type="date"
                  value={endDateFilter}
                  onChange={(e) => setEndDateFilter(e.target.value)}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors"
                />
              </div>
            </div>
          </div>
        )}

        {/* Filter Summary - REMOVED */}
        {/* This section was displaying "122 of 122 employees found" */}
      </div>
    </div>
  );
};

export default LeaveFilters; 